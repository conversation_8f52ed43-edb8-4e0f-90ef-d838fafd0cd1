import React from 'react';
import { useTranslation } from 'react-i18next';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  Target, 
  Eye, 
  Users, 
  MapPin, 
  Award,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react';

export default function AboutPage() {
  const { t } = useTranslation();

  const values = [
    {
      icon: Shield,
      title: 'Quality Assurance',
      description: 'We ensure all products meet or exceed OEM specifications for your peace of mind.'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'Leveraging cutting-edge technology to revolutionize the auto parts industry in Algeria.'
    },
    {
      icon: Users,
      title: 'Customer First',
      description: 'Our customers are at the heart of everything we do, driving our commitment to excellence.'
    },
    {
      icon: TrendingUp,
      title: 'Growth',
      description: 'Empowering local businesses and manufacturers to grow and thrive in the digital economy.'
    }
  ];

  const stats = [
    { number: '10,000+', label: 'Products Available' },
    { number: '500+', label: 'Partner Suppliers' },
    { number: '48', label: 'Wilayas Covered' },
    { number: '24/7', label: 'Customer Support' }
  ];

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-[#071c44] mb-6">
            About AROUZ MARKET
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Algeria's First Intelligent Auto Parts Ecosystem connecting manufacturers, 
            suppliers, merchants, and consumers in one unified platform.
          </p>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          <Card className="bg-gradient-to-br from-[#071c44] to-[#071c44]/80 text-white">
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <Target className="h-8 w-8 text-[#DC2626] mr-3" />
                <h2 className="text-2xl font-bold">Our Mission</h2>
              </div>
              <p className="text-gray-200 leading-relaxed">
                To digitize and streamline the auto parts supply chain in Algeria, 
                empowering local manufacturers and creating a more efficient marketplace 
                that serves all stakeholders from suppliers to end consumers.
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-[#DC2626] to-[#DC2626]/80 text-white">
            <CardContent className="p-8">
              <div className="flex items-center mb-6">
                <Eye className="h-8 w-8 text-white mr-3" />
                <h2 className="text-2xl font-bold">Our Vision</h2>
              </div>
              <p className="text-orange-100 leading-relaxed">
                To become the leading digital platform that transforms Algeria's auto parts 
                industry, reducing import dependency and fostering a thriving local ecosystem 
                built on innovation and efficiency.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Stats Section */}
        <div className="bg-gray-50 rounded-2xl p-8 mb-16">
          <h2 className="text-3xl font-bold text-[#071c44] text-center mb-8">
            AROUZ MARKET by the Numbers
          </h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-[#DC2626] mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Our Values */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-[#071c44] text-center mb-12">
            Our Core Values
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-[#DC2626]/10 p-3 rounded-lg">
                      <value.icon className="h-6 w-6 text-[#DC2626]" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-[#071c44] mb-3">
                        {value.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Our Story */}
        <div className="mb-16">
          <Card className="bg-white border-0 shadow-lg">
            <CardContent className="p-8 md:p-12">
              <div className="max-w-4xl mx-auto">
                <h2 className="text-3xl font-bold text-[#071c44] mb-8 text-center">
                  Our Story
                </h2>
                <div className="prose prose-lg max-w-none text-gray-600">
                  <p className="mb-6">
                    AROUZ MARKET was born from a simple observation: Algeria's auto parts 
                    industry was fragmented, inefficient, and heavily dependent on imports. 
                    We saw an opportunity to create a unified digital ecosystem that would 
                    connect all stakeholders and streamline operations.
                  </p>
                  <p className="mb-6">
                    Starting with a vision to digitize the entire supply chain, we built 
                    a platform that serves manufacturers, suppliers, merchants, and consumers 
                    alike. Our intelligent matching system, comprehensive inventory management, 
                    and seamless marketplace experience have transformed how auto parts are 
                    bought and sold in Algeria.
                  </p>
                  <p>
                    Today, AROUZ MARKET stands as Algeria's premier auto parts platform, 
                    empowering local businesses, reducing costs, and providing consumers 
                    with unprecedented access to quality parts and services.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Location */}
        <div className="mb-16">
          <Card className="bg-gradient-to-r from-[#071c44] to-[#071c44]/90 text-white">
            <CardContent className="p-8 text-center">
              <MapPin className="h-12 w-12 text-[#DC2626] mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">Proudly Algerian</h2>
              <p className="text-gray-200 max-w-2xl mx-auto">
                Based in Algiers, we serve customers across all 48 wilayas of Algeria, 
                bringing the future of auto parts commerce to every corner of our nation.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="bg-[#DC2626] text-white">
            <CardContent className="py-12">
              <h3 className="text-3xl font-bold mb-4">Join the AROUZ MARKET Family</h3>
              <p className="text-orange-100 mb-8 max-w-2xl mx-auto">
                Whether you're a supplier, merchant, or consumer, become part of Algeria's 
                most innovative auto parts ecosystem.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-white text-[#DC2626] hover:bg-gray-100" asChild>
                  <Link to="/partners">
                    Become a Partner
                  </Link>
                </Button>
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-[#DC2626]" asChild>
                  <Link to="/contact">
                    Contact Us
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MarketplaceLayout>
  );
}
