import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getProductById, fetchMarketplaceProductsByCategory } from '@/services/productService';
import { getAllProducts } from '@/utils/centralizedProductData';
import { isValidProductId } from '@/utils/productUtils';

export default function ProductPageDiagnostic() {
  const { productId } = useParams<{ productId: string }>();
  const [diagnosticData, setDiagnosticData] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runDiagnostics = async () => {
      console.log('🔍 DIAGNOSTIC: Starting comprehensive ProductPage diagnostics');

      const results: any = {
        timestamp: new Date().toISOString(),
        productId: productId,
        tests: {}
      };

      // Test 1: Component Rendering
      results.tests.componentRendering = {
        status: 'PASS',
        message: 'ProductPageDiagnostic component is rendering correctly'
      };

      // Test 2: URL Parameter Extraction
      results.tests.urlParameter = {
        status: productId ? 'PASS' : 'FAIL',
        productId: productId,
        message: productId ? `Product ID extracted: ${productId}` : 'No product ID in URL'
      };

      // Test 3: Product ID Validation
      if (productId) {
        const isValid = isValidProductId(productId);
        results.tests.idValidation = {
          status: isValid ? 'PASS' : 'FAIL',
          isValid: isValid,
          message: isValid ? 'Product ID format is valid' : 'Product ID format is invalid'
        };
      }

      // Test 4: Centralized Data Access
      try {
        const allProducts = getAllProducts();
        results.tests.centralizedData = {
          status: 'PASS',
          totalProducts: allProducts.length,
          sampleIds: allProducts.slice(0, 5).map(p => p.id),
          message: `Successfully loaded ${allProducts.length} products from centralized data`
        };

        // Test 5: Product Lookup in Centralized Data
        if (productId) {
          const foundInCentralized = allProducts.find(p => p.id === productId);
          results.tests.centralizedLookup = {
            status: foundInCentralized ? 'PASS' : 'FAIL',
            found: !!foundInCentralized,
            productName: foundInCentralized?.name,
            message: foundInCentralized
              ? `Product found in centralized data: ${foundInCentralized.name}`
              : 'Product not found in centralized data'
          };
        }
      } catch (error) {
        results.tests.centralizedData = {
          status: 'FAIL',
          error: error.message,
          message: 'Failed to access centralized data'
        };
      }

      // Test 6: Real Marketplace Data Access
      try {
        console.log('🔍 DIAGNOSTIC: Testing real marketplace data');
        const tyreProducts = await fetchMarketplaceProductsByCategory('tyres');
        const brakeProducts = await fetchMarketplaceProductsByCategory('brakes');
        const allMarketplaceProducts = [...tyreProducts, ...brakeProducts];

        results.tests.marketplaceData = {
          status: allMarketplaceProducts.length > 0 ? 'PASS' : 'FAIL',
          totalProducts: allMarketplaceProducts.length,
          tyreCount: tyreProducts.length,
          brakeCount: brakeProducts.length,
          sampleIds: allMarketplaceProducts.slice(0, 10).map(p => p.id),
          message: `Found ${allMarketplaceProducts.length} real marketplace products (${tyreProducts.length} tyres, ${brakeProducts.length} brakes)`
        };

        // Test 7: Product Lookup in Real Marketplace Data
        if (productId) {
          const foundInMarketplace = allMarketplaceProducts.find(p => p.id === productId);
          results.tests.marketplaceLookup = {
            status: foundInMarketplace ? 'PASS' : 'FAIL',
            found: !!foundInMarketplace,
            productName: foundInMarketplace?.name,
            productCategory: foundInMarketplace?.category,
            productSection: foundInMarketplace?.marketplaceSection,
            message: foundInMarketplace
              ? `Product found in marketplace data: ${foundInMarketplace.name} (${foundInMarketplace.category}/${foundInMarketplace.marketplaceSection})`
              : 'Product not found in marketplace data'
          };
        }
      } catch (error) {
        results.tests.marketplaceData = {
          status: 'FAIL',
          error: error.message,
          message: 'Failed to access marketplace data'
        };
      }

      // Test 8: Service Layer Data Fetching
      if (productId) {
        try {
          console.log('🔍 DIAGNOSTIC: Testing getProductById service');
          const serviceResult = await getProductById(productId);
          results.tests.serviceFetch = {
            status: serviceResult ? 'PASS' : 'FAIL',
            found: !!serviceResult,
            productName: serviceResult?.name,
            productCategory: serviceResult?.category,
            productSection: serviceResult?.marketplaceSection,
            message: serviceResult
              ? `Service successfully returned product: ${serviceResult.name} (${serviceResult.category}/${serviceResult.marketplaceSection})`
              : 'Service returned null/undefined'
          };
        } catch (error) {
          results.tests.serviceFetch = {
            status: 'FAIL',
            error: error.message,
            message: 'Service layer threw an error'
          };
        }
      }

      // Test 9: React Router Integration
      results.tests.routing = {
        status: 'PASS',
        currentPath: window.location.pathname,
        expectedPattern: '/:productId',
        message: 'React Router is working - component loaded via dynamic route'
      };

      console.log('🔍 DIAGNOSTIC: Complete results:', results);
      setDiagnosticData(results);
      setLoading(false);
    };

    runDiagnostics();
  }, [productId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PASS': return 'bg-green-100 text-green-800 border-green-200';
      case 'FAIL': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const testRealProductIds = () => {
    const realIds = [
      'TYR-100000-WHOLESALE',
      'TYR-100000-RETAIL',
      'TYR-100001-WHOLESALE',
      'TYR-100001-RETAIL',
      'BRK-100000-WHOLESALE',
      'BRK-100000-RETAIL'
    ];

    realIds.forEach(id => {
      window.open(`/${id}`, '_blank');
    });
  };

  const testMarketplaceProducts = () => {
    const marketplaceIds = diagnosticData.tests?.marketplaceData?.sampleIds || [];
    if (marketplaceIds.length === 0) {
      alert('No marketplace products found. Please run the diagnostic first.');
      return;
    }

    marketplaceIds.slice(0, 5).forEach((id: string) => {
      window.open(`/${id}`, '_blank');
    });
  };

  if (loading) {
    return (
      <MarketplaceLayout>
        <div className="container py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Running ProductPage Diagnostics...</h1>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#DC2626] mx-auto"></div>
          </div>
        </div>
      </MarketplaceLayout>
    );
  }

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">ProductPage Diagnostic Report</h1>
            <p className="text-gray-600">
              Comprehensive analysis of ProductPage functionality and data flow
            </p>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Test URL:</strong> {window.location.href}
              </p>
              <p className="text-sm text-blue-800">
                <strong>Timestamp:</strong> {diagnosticData.timestamp}
              </p>
            </div>
          </div>

          {/* Test Results */}
          <div className="space-y-4 mb-8">
            {Object.entries(diagnosticData.tests || {}).map(([testName, result]: [string, any]) => (
              <Card key={testName}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between">
                    <span className="capitalize">{testName.replace(/([A-Z])/g, ' $1').trim()}</span>
                    <Badge className={getStatusColor(result.status)}>
                      {result.status}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 mb-2">{result.message}</p>
                  {result.error && (
                    <div className="mt-2 p-2 bg-red-50 rounded text-xs text-red-700">
                      <strong>Error:</strong> {result.error}
                    </div>
                  )}
                  {result.productName && (
                    <div className="mt-2 p-2 bg-green-50 rounded text-xs text-green-700">
                      <strong>Product:</strong> {result.productName}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Test Real Marketplace Products</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Click to test actual products from the marketplace database:
                </p>
                <div className="flex gap-3">
                  <Button onClick={testMarketplaceProducts} className="bg-[#DC2626] hover:bg-[#DC2626]/90">
                    Test Marketplace Products
                  </Button>
                  <Button onClick={testRealProductIds} variant="outline">
                    Test Mock Product IDs
                  </Button>
                </div>
              </CardContent>
            </Card>

            <div className="flex space-x-4">
              <Button
                variant="outline"
                onClick={() => window.location.href = '/product-debug'}
              >
                View Product Debug Page
              </Button>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
              >
                Back to Marketplace
              </Button>
            </div>
          </div>
        </div>
      </div>
    </MarketplaceLayout>
  );
}
