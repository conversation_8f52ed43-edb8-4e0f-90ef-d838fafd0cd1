import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { FilterProvider } from '@/contexts/FilterContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Package,
  ArrowLeft,
  Calendar,
  MapPin,
  Phone,
  Eye,
  X,
  Truck,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { getConsumerOrders, Order } from '@/services/orderService';
import { ConsumerAuthGuard } from '@/components/auth/ConsumerAuthGuard';

export default function MyOrdersPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);

  // Check authentication and load orders
  useEffect(() => {
    const checkAuthAndLoadOrders = async () => {
      try {
        // Check authentication
        const phoneSession = localStorage.getItem('phone_auth_session');
        const isAuth = phoneSession && JSON.parse(phoneSession).profile?.role === 'consumer';
        
        if (!isAuth) {
          navigate('/');
          return;
        }
        
        setIsConsumerAuthenticated(true);
        
        // Load orders
        const result = await getConsumerOrders();
        if (result.success) {
          setOrders(result.orders || []);
        } else {
          toast({
            title: 'Failed to load orders',
            description: result.error || 'Please try again',
            variant: 'destructive'
          });
        }
      } catch (error) {
        console.error('Error loading orders:', error);
        toast({
          title: 'Failed to load orders',
          description: 'Please try again',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthAndLoadOrders();
  }, [navigate, toast]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'shipped':
        return <Truck className="h-4 w-4 text-purple-600" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'cancelled':
        return <X className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-DZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (isLoading) {
    return (
      <FilterProvider>
        <MarketplaceLayout>
          <div className="min-h-screen bg-gray-50">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="space-y-6">
                <Skeleton className="h-8 w-48" />
                {[1, 2, 3].map((i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <Skeleton className="h-6 w-32" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </MarketplaceLayout>
      </FilterProvider>
    );
  }

  return (
    <ConsumerAuthGuard
      action="orders"
      fallback={
        <FilterProvider>
          <MarketplaceLayout>
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="text-center">
                <Package className="h-16 w-16 text-[#DC2626] mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Your Orders</h2>
                <p className="text-gray-600 mb-6">View and track your order history.</p>
                <button
                  onClick={() => {
                    const authModal = document.getElementById('auth-modal-trigger');
                    if (authModal) {
                      authModal.click();
                    }
                  }}
                  className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white px-6 py-3 rounded-lg font-medium"
                >
                  {t('auth.loginOrSignUp')}
                </button>
              </div>
            </div>
          </MarketplaceLayout>
        </FilterProvider>
      }
    >
      <FilterProvider>
        <MarketplaceLayout>
          <div className="min-h-screen bg-gray-50">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {/* Header */}
              <div className="mb-8">
                <div className="flex items-center gap-4 mb-4">
                  <Button
                    variant="ghost"
                    onClick={() => navigate('/')}
                    className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    {t('actions.back')}
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                      <Package className="h-8 w-8 text-[#DC2626]" />
                      My Orders
                    </h1>
                    <p className="text-gray-600 mt-2">
                      {orders.length} order{orders.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
              </div>

              {/* Orders List */}
              <div className="space-y-6">
                {orders.length === 0 ? (
                  <Card>
                    <CardContent className="p-12 text-center">
                      <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">No orders yet</h3>
                      <p className="text-gray-600 mb-6">Start shopping to see your orders here.</p>
                      <Button
                        onClick={() => navigate('/')}
                        className="bg-[#DC2626] hover:bg-[#DC2626]/90"
                      >
                        Start Shopping
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  orders.map((order) => (
                    <Card key={order.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-lg font-semibold">
                              Order #{order.order_number}
                            </CardTitle>
                            <p className="text-sm text-gray-600 flex items-center gap-2 mt-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(order.created_at)}
                            </p>
                          </div>
                          <div className="flex items-center gap-3">
                            <Badge className={`${getStatusColor(order.status)} flex items-center gap-1`}>
                              {getStatusIcon(order.status)}
                              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <MapPin className="h-4 w-4" />
                            <span>{order.delivery_address}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Truck className="h-4 w-4" />
                            <span>{order.payment_method === 'cash_on_delivery' ? 'Cash on Delivery' : 'Store Pickup'}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between pt-4 border-t">
                          <div>
                            <p className="text-lg font-semibold text-[#DC2626]">
                              {formatPrice(order.total_amount)}
                            </p>
                            <p className="text-sm text-gray-600">Total Amount</p>
                          </div>
                          <Button
                            onClick={() => navigate(`/order-success?orderId=${order.id}`)}
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>
          </div>
        </MarketplaceLayout>
      </FilterProvider>
    </ConsumerAuthGuard>
  );
}
