import React from 'react';
import { useTranslation } from 'react-i18next';
import { PublicLayout } from '@/components/layout/PublicLayout';
import { TeamMember } from '@/components/public/TeamMember';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';

export default function AboutPage() {
  const { t } = useTranslation();
  
  return (
    <PublicLayout>
      {/* Hero Section */}
      <section className="py-20 bg-muted/30">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold text-midnight-blue mb-6">
              {t('public.aboutTitle')}
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              {t('public.aboutSubtitle')}
            </p>
          </div>
        </div>
      </section>
      
      {/* Vision & Mission Section */}
      <section className="py-20">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div>
              <div className="inline-flex items-center rounded-full border border-electric-orange/30 bg-electric-orange/10 px-3 py-1 text-sm text-electric-orange mb-4">
                <span className="font-medium">
                  {t('public.ourVision')}
                </span>
              </div>
              <h2 className="text-3xl font-bold text-midnight-blue mb-4">
                {t('public.visionTitle')}
              </h2>
              <p className="text-muted-foreground mb-6">
                {t('public.visionDescription')}
              </p>
            </div>
            
            <div>
              <div className="inline-flex items-center rounded-full border border-electric-orange/30 bg-electric-orange/10 px-3 py-1 text-sm text-electric-orange mb-4">
                <span className="font-medium">
                  {t('public.ourMission')}
                </span>
              </div>
              <h2 className="text-3xl font-bold text-midnight-blue mb-4">
                {t('public.missionTitle')}
              </h2>
              <p className="text-muted-foreground mb-6">
                {t('public.missionDescription')}
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Differentiator Section */}
      <section className="py-20 bg-midnight-blue text-white">
        <div className="container">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">
              {t('public.differentiatorTitle')}
            </h2>
            <p className="text-xl text-white/70 mb-8">
              {t('public.differentiatorDescription')}
            </p>
            <Button
              className="bg-[#DC2626] hover:bg-[#B91C1C] text-white"
              asChild
            >
              <Link to="/features">
                {t('public.explorePlatform')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
      
      {/* Team Section */}
      <section className="py-20">
        <div className="container">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-midnight-blue mb-4">
              {t('public.meetTheFounders')}
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('public.foundersDescription')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <TeamMember
              name="Hamza Aroussi"
              role={t('public.roboticsEngineer')}
              bio={t('public.hamzaBio')}
              linkedinUrl="https://linkedin.com/"
              githubUrl="https://github.com/"
            />
            
            <TeamMember
              name="Ahmed Henine"
              role={t('public.computerScientist')}
              bio={t('public.ahmedBio')}
              linkedinUrl="https://linkedin.com/"
              githubUrl="https://github.com/"
            />
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-lg font-medium text-midnight-blue">
              {t('public.algerianTeam')}
            </p>
          </div>
        </div>
      </section>
    </PublicLayout>
  );
}
