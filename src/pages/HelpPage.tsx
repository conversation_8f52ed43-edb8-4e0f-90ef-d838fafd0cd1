import React from 'react';
import { useTranslation } from 'react-i18next';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  HelpCircle, 
  MessageCircle, 
  Phone, 
  Mail, 
  ArrowRight,
  Search,
  Package,
  CreditCard,
  Truck
} from 'lucide-react';

export default function HelpPage() {
  const { t } = useTranslation();

  const helpCategories = [
    {
      icon: Package,
      title: 'Orders & Shipping',
      description: 'Track orders, shipping info, and delivery questions',
      link: '/my-orders'
    },
    {
      icon: CreditCard,
      title: 'Payment & Billing',
      description: 'Payment methods, billing, and refund information',
      link: '/help/payment'
    },
    {
      icon: Search,
      title: 'Finding Parts',
      description: 'How to search and find the right parts for your vehicle',
      link: '/help/finding-parts'
    },
    {
      icon: Truck,
      title: 'Returns & Exchanges',
      description: 'Return policy, exchanges, and warranty information',
      link: '/help/returns'
    }
  ];

  const faqs = [
    {
      question: 'How do I track my order?',
      answer: 'You can track your order by visiting the "My Orders" page or using the tracking link sent to your phone.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'Currently, we accept cash on delivery and store pickup payments. Online payment options will be available soon.'
    },
    {
      question: 'How long does delivery take?',
      answer: 'Most orders are delivered within 24-48 hours depending on your location and product availability.'
    },
    {
      question: 'Can I return a product?',
      answer: 'Yes, we have a comprehensive return policy. Please contact our support team for assistance with returns.'
    }
  ];

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-[#071c44] mb-4">
            {t('footer.help')} & Support
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find answers to your questions and get the help you need
          </p>
        </div>

        {/* Quick Contact */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Phone className="h-12 w-12 text-[#DC2626] mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Call Us</h3>
              <p className="text-gray-600 text-sm mb-4">
                Get immediate help from our support team
              </p>
              <Button variant="outline" size="sm">
                +213 XX XX XX XX
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <MessageCircle className="h-12 w-12 text-[#DC2626] mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Live Chat</h3>
              <p className="text-gray-600 text-sm mb-4">
                Chat with our support team in real-time
              </p>
              <Button variant="outline" size="sm">
                Start Chat
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <Mail className="h-12 w-12 text-[#DC2626] mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Email Support</h3>
              <p className="text-gray-600 text-sm mb-4">
                Send us an email and we'll respond within 24 hours
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link to="/contact">
                  Send Email
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Help Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-[#071c44] mb-6">Browse Help Topics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {helpCategories.map((category, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-[#DC2626]/10 p-3 rounded-lg">
                      <category.icon className="h-6 w-6 text-[#DC2626]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-2">{category.title}</h3>
                      <p className="text-gray-600 mb-4">{category.description}</p>
                      <Button variant="ghost" size="sm" asChild>
                        <Link to={category.link} className="text-[#DC2626] hover:text-[#DC2626]/80">
                          Learn More <ArrowRight className="h-4 w-4 ml-1" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div>
          <h2 className="text-2xl font-bold text-[#071c44] mb-6">Frequently Asked Questions</h2>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <HelpCircle className="h-5 w-5 text-[#DC2626] mr-2" />
                    {faq.question}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Still Need Help */}
        <div className="mt-12 text-center">
          <Card className="bg-[#071c44] text-white">
            <CardContent className="py-12">
              <h3 className="text-2xl font-bold mb-4">Still Need Help?</h3>
              <p className="text-gray-300 mb-6 max-w-md mx-auto">
                Can't find what you're looking for? Our support team is here to help you.
              </p>
              <Button className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white" asChild>
                <Link to="/contact">
                  Contact Support
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </MarketplaceLayout>
  );
}
