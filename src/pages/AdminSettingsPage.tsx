import React from 'react';
import { AdminPanelLayout } from '@/components/layout/AdminPanelLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings, Construction } from 'lucide-react';

export default function AdminSettingsPage() {
  return (
    <AdminPanelLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600 mt-1">
            Configure system settings and preferences
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Construction className="h-5 w-5 text-[#DC2626]" />
              Coming Soon
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Settings className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                System Settings
              </h3>
              <p className="text-gray-600">
                This section will provide system configuration options, 
                marketplace settings, and administrative preferences.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}
