/**
 * My Reviews Page - Consumer Reviews Management
 * 
 * Displays and manages consumer's product reviews
 * 🎯 CRITICAL: Preserves existing authentication system - integrates with phone auth
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { FilterProvider } from '@/contexts/FilterContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Star,
  ArrowLeft,
  MessageSquare,
  Calendar,
  Package,
  Edit,
  Trash2
} from 'lucide-react';
import { getConsumerReviews, deleteReview, ConsumerReview } from '@/services/reviewsService';
import { ConsumerAuthGuard } from '@/components/auth/ConsumerAuthGuard';

export default function MyReviewsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [reviews, setReviews] = useState<ConsumerReview[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);
  const [deletingReviews, setDeletingReviews] = useState<Set<string>>(new Set());

  // Check authentication and load reviews
  useEffect(() => {
    const checkAuthAndLoadReviews = async () => {
      try {
        // Check authentication
        const phoneSession = localStorage.getItem('phone_auth_session');
        const isAuth = phoneSession && JSON.parse(phoneSession).profile?.role === 'consumer';
        
        if (!isAuth) {
          navigate('/');
          return;
        }
        
        setIsConsumerAuthenticated(true);
        
        // Load reviews
        const result = await getConsumerReviews();
        if (result.success) {
          setReviews(result.reviews || []);
        } else {
          toast.error('Failed to load reviews', {
            description: result.error || 'Please try again'
          });
        }
      } catch (error) {
        console.error('Error loading reviews:', error);
        toast.error('Failed to load reviews', {
          description: 'Please try again'
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthAndLoadReviews();
  }, [navigate, toast, t]);

  // Handle delete review
  const handleDeleteReview = async (reviewId: string) => {
    setDeletingReviews(prev => new Set(prev).add(reviewId));
    
    try {
      const result = await deleteReview(reviewId);
      if (result.success) {
        setReviews(prev => prev.filter(review => review.id !== reviewId));
        toast.success('Review deleted', {
          description: 'Your review has been deleted successfully'
        });
      } else {
        toast.error('Failed to delete review', {
          description: result.error || 'Please try again'
        });
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review', {
        description: 'Please try again'
      });
    } finally {
      setDeletingReviews(prev => {
        const newSet = new Set(prev);
        newSet.delete(reviewId);
        return newSet;
      });
    }
  };

  // Navigate to product page
  const handleProductClick = (productId: string) => {
    navigate(`/${productId}`);
  };

  // Render star rating
  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'fill-gray-200 text-gray-200'
            }`}
          />
        ))}
      </div>
    );
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!isConsumerAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <ConsumerAuthGuard
      action="review"
      fallback={
        <FilterProvider>
          <MarketplaceLayout>
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="text-center">
                <Star className="h-16 w-16 text-[#DC2626] mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Your Reviews</h2>
                <p className="text-gray-600 mb-6">View and manage your product reviews.</p>
                <button
                  onClick={() => {
                    const authModal = document.getElementById('auth-modal-trigger');
                    if (authModal) {
                      authModal.click();
                    }
                  }}
                  className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white px-6 py-3 rounded-lg font-medium"
                >
                  {t('auth.loginOrSignUp')}
                </button>
              </div>
            </div>
          </MarketplaceLayout>
        </FilterProvider>
      }
    >
      <FilterProvider>
        <MarketplaceLayout>
          <div className="min-h-screen bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant="ghost"
                  onClick={() => navigate('/')}
                  className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="h-4 w-4" />
                  {t('actions.back')}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                    <MessageSquare className="h-8 w-8 text-[#DC2626]" />
                    {t('marketplace.myReviews')}
                  </h1>
                  <p className="text-gray-600 mt-2">
                    {reviews.length} review{reviews.length !== 1 ? 's' : ''}
                  </p>
                </div>
              </div>
            </div>

            {/* Content */}
            {isLoading ? (
              <div className="space-y-6">
                {[...Array(5)].map((_, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Skeleton className="h-6 w-3/4 mb-2" />
                          <Skeleton className="h-4 w-1/2 mb-2" />
                          <Skeleton className="h-4 w-1/4" />
                        </div>
                        <Skeleton className="h-8 w-20" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-3/4" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : reviews.length === 0 ? (
              <div className="text-center py-16">
                <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No Reviews Yet
                </h3>
                <p className="text-gray-600 mb-6">
                  You haven't written any reviews yet. Start shopping and share your experience!
                </p>
                <Button
                  onClick={() => navigate('/')}
                  className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white"
                >
                  {t('marketplace.continueShopping')}
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                {reviews.map((review) => (
                  <Card key={review.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle 
                            className="text-lg cursor-pointer hover:text-[#DC2626] transition-colors"
                            onClick={() => handleProductClick(review.product_id)}
                          >
                            {review.product_name}
                          </CardTitle>
                          <div className="flex items-center gap-3 mt-2">
                            {renderStars(review.rating)}
                            <span className="text-sm text-gray-500">
                              {review.rating}/5
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                            <Calendar className="h-4 w-4" />
                            {formatDate(review.created_at)}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Badge variant={review.is_approved ? "default" : "secondary"}>
                            {review.is_approved ? 'Published' : 'Pending Review'}
                          </Badge>
                          {review.is_verified_purchase && (
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              Verified Purchase
                            </Badge>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteReview(review.id)}
                            disabled={deletingReviews.has(review.id)}
                            className="text-red-500 hover:text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      {review.title && (
                        <h4 className="font-semibold text-gray-900 mb-2">
                          {review.title}
                        </h4>
                      )}
                      {review.review_text && (
                        <p className="text-gray-700 leading-relaxed">
                          {review.review_text}
                        </p>
                      )}
                      
                      {review.helpful_count > 0 && (
                        <div className="mt-4 text-sm text-gray-500">
                          {review.helpful_count} people found this helpful
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
            </div>
          </div>
        </MarketplaceLayout>
      </FilterProvider>
    </ConsumerAuthGuard>
  );
}
