import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { MapPin, CheckCircle, XCircle, AlertTriangle, Target } from 'lucide-react';
import { findAccurateWilayaByCoordinates } from '@/data/algeria-accurate-location';
import { findAccuratePreciseWilayaByCoordinates } from '@/data/algeria-precise-location';

/**
 * NORTHWEST BORDER FIX VERIFICATION PAGE
 * 
 * This page allows testing the specific coordinate 34.834096, -1.670609
 * that was incorrectly being detected as Tlemcen to verify our fix.
 */

interface TestResult {
  coordinates: { lat: number; lng: number };
  accurate: any;
  precise: any;
  testName: string;
  expected: string;
  status: 'pass' | 'fail' | 'unknown';
}

export default function TestNorthwestFix() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [customLat, setCustomLat] = useState('34.834096');
  const [customLng, setCustomLng] = useState('-1.670609');

  // Critical test cases for northwest region
  const criticalTests = [
    {
      name: "🚨 CRITICAL: User reported coordinate",
      lat: 34.834096,
      lng: -1.670609,
      expected: "Tlemcen",
      description: "The exact coordinate from user's screenshot"
    },
    {
      name: "Tlemcen center verification",
      lat: 34.8786,
      lng: -1.3150,
      expected: "Tlemcen",
      description: "Should definitely be Tlemcen (center)"
    },
    {
      name: "Sidi Bel Abbès center",
      lat: 35.1977,
      lng: -0.6388,
      expected: "Sidi Bel Abbès",
      description: "Should definitely be Sidi Bel Abbès"
    },
    {
      name: "Border test: West of Tlemcen",
      lat: 34.8,
      lng: -2.0,
      expected: "Tlemcen or Naâma",
      description: "Far west border area"
    },
    {
      name: "Border test: Between Tlemcen and Sidi Bel Abbès",
      lat: 35.0,
      lng: -1.0,
      expected: "Tlemcen or Sidi Bel Abbès",
      description: "Border area between wilayas"
    }
  ];

  const runCriticalTests = async () => {
    setIsLoading(true);
    const results: TestResult[] = [];

    for (const test of criticalTests) {
      // Test both detection systems
      const accurateResult = findAccurateWilayaByCoordinates(test.lat, test.lng);
      const preciseResult = await findAccuratePreciseWilayaByCoordinates(test.lat, test.lng);

      let status: 'pass' | 'fail' | 'unknown' = 'unknown';
      
      // Check if results match expected
      if (test.expected.includes('or')) {
        // Multiple valid options
        const validOptions = test.expected.split(' or ');
        status = validOptions.some(option => 
          accurateResult?.name === option || preciseResult?.name === option
        ) ? 'pass' : 'fail';
      } else {
        // Single expected result
        status = (accurateResult?.name === test.expected && preciseResult?.name === test.expected) 
          ? 'pass' : 'fail';
      }

      results.push({
        coordinates: { lat: test.lat, lng: test.lng },
        accurate: accurateResult,
        precise: preciseResult,
        testName: test.name,
        expected: test.expected,
        status
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  const testCustomCoordinate = async () => {
    const lat = parseFloat(customLat);
    const lng = parseFloat(customLng);

    if (isNaN(lat) || isNaN(lng)) {
      alert('Please enter valid coordinates');
      return;
    }

    setIsLoading(true);

    const accurateResult = findAccurateWilayaByCoordinates(lat, lng);
    const preciseResult = await findAccuratePreciseWilayaByCoordinates(lat, lng);

    const customResult: TestResult = {
      coordinates: { lat, lng },
      accurate: accurateResult,
      precise: preciseResult,
      testName: "Custom coordinate test",
      expected: "Unknown",
      status: 'unknown'
    };

    setTestResults([customResult, ...testResults]);
    setIsLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fail': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800';
      case 'fail': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  useEffect(() => {
    // Auto-run tests on page load
    runCriticalTests();
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-orange-500" />
            Northwest Border Fix Verification
          </CardTitle>
          <p className="text-sm text-gray-600">
            Testing the fix for coordinate 34.834096, -1.670609 that was incorrectly detected
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Control Panel */}
          <div className="flex flex-wrap gap-4 items-end">
            <div className="space-y-2">
              <label className="text-sm font-medium">Latitude</label>
              <Input
                value={customLat}
                onChange={(e) => setCustomLat(e.target.value)}
                placeholder="34.834096"
                className="w-32"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Longitude</label>
              <Input
                value={customLng}
                onChange={(e) => setCustomLng(e.target.value)}
                placeholder="-1.670609"
                className="w-32"
              />
            </div>
            <Button onClick={testCustomCoordinate} disabled={isLoading}>
              Test Custom
            </Button>
            <Button onClick={runCriticalTests} disabled={isLoading} variant="outline">
              {isLoading ? 'Testing...' : 'Run All Tests'}
            </Button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-semibold">Test Results:</h3>
              {testResults.map((result, index) => (
                <Card key={index} className="border-l-4 border-l-red-500">
                  <CardContent className="pt-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(result.status)}
                          <span className="font-medium">{result.testName}</span>
                          <Badge className={getStatusColor(result.status)}>
                            {result.status.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <p><strong>Coordinates:</strong> {result.coordinates.lat}, {result.coordinates.lng}</p>
                          <p><strong>Expected:</strong> {result.expected}</p>
                          
                          <div className="bg-blue-50 p-3 rounded">
                            <p className="font-medium text-blue-800">Accurate Detection:</p>
                            <p>Wilaya: {result.accurate?.name || 'NULL'} ({result.accurate?.name_ar || 'N/A'})</p>
                            <p>Confidence: {((result.accurate?.confidence || 0) * 100).toFixed(1)}%</p>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="bg-green-50 p-3 rounded">
                            <p className="font-medium text-green-800">Precise Detection:</p>
                            <p>Wilaya: {result.precise?.name || 'NULL'} ({result.precise?.name_ar || 'N/A'})</p>
                            <p>Confidence: {((result.precise?.confidence || 0) * 100).toFixed(1)}%</p>
                            <p>Method: {result.precise?.method || 'N/A'}</p>
                            <p>Accuracy: {result.precise?.accuracy || 'N/A'}</p>
                          </div>
                        </div>
                      </div>

                      {/* Special analysis for the critical coordinate */}
                      {result.testName.includes('CRITICAL') && (
                        <div className="bg-red-50 p-3 rounded border-l-4 border-l-red-500">
                          <p className="font-medium text-orange-800">🔍 Critical Analysis:</p>
                          <p className="text-sm">
                            This is the exact coordinate from your screenshot. 
                            {result.accurate?.name === 'Tlemcen' && result.precise?.name === 'Tlemcen' 
                              ? ' ✅ Both systems now correctly detect it as Tlemcen.'
                              : ' ❌ Detection mismatch - needs further investigation.'
                            }
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
