import React, { useState, useEffect } from 'react';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  checkMigrationStatus, 
  migrateAllProducts,
  migrateCategoryProducts,
  clearLocalStorageAfterMigration,
  rollbackMigration,
  MigrationSummary,
  MigrationResult
} from '@/utils/supabaseMigration';
import { 
  Database, 
  Upload, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw, 
  Trash2,
  ArrowRight,
  Cloud,
  HardDrive
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

export default function SupabaseMigrationPage() {
  const { isAuthenticated } = useAuth();
  const [migrationStatus, setMigrationStatus] = useState<{
    isAuthenticated: boolean;
    localStorageData: { [category: string]: number };
    supabaseData: { [category: string]: number };
    needsMigration: boolean;
  } | null>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [migrationResult, setMigrationResult] = useState<MigrationSummary | null>(null);
  const [currentOperation, setCurrentOperation] = useState<string>('');

  // Check migration status on component mount
  useEffect(() => {
    if (isAuthenticated) {
      checkStatus();
    }
  }, [isAuthenticated]);

  const checkStatus = async () => {
    try {
      setIsLoading(true);
      const status = await checkMigrationStatus();
      setMigrationStatus(status);
    } catch (error) {
      console.error('Error checking migration status:', error);
      toast.error('Failed to check migration status');
    } finally {
      setIsLoading(false);
    }
  };

  const runFullMigration = async () => {
    if (!migrationStatus?.isAuthenticated) {
      toast.error('Please log in to perform migration');
      return;
    }

    setIsLoading(true);
    setCurrentOperation('Migrating all products to Supabase...');
    
    try {
      const result = await migrateAllProducts();
      setMigrationResult(result);
      
      if (result.totalMigrated > 0) {
        toast.success(`Successfully migrated ${result.totalMigrated} products!`, {
          description: `Duration: ${(result.duration / 1000).toFixed(1)}s`,
        });
      } else if (result.totalSkipped > 0) {
        toast.info('All products already exist in Supabase');
      } else {
        toast.warning('No products were migrated');
      }
      
      // Refresh status after migration
      await checkStatus();
    } catch (error) {
      console.error('Migration error:', error);
      toast.error('Migration failed. Check console for details.');
    } finally {
      setIsLoading(false);
      setCurrentOperation('');
    }
  };

  const runCategoryMigration = async (category: string) => {
    if (!migrationStatus?.isAuthenticated) {
      toast.error('Please log in to perform migration');
      return;
    }

    setIsLoading(true);
    setCurrentOperation(`Migrating ${category} products...`);
    
    try {
      const result = await migrateCategoryProducts(category);
      
      if (result.migratedProducts > 0) {
        toast.success(`Successfully migrated ${result.migratedProducts} ${category} products!`);
      } else if (result.skippedProducts > 0) {
        toast.info(`All ${category} products already exist in Supabase`);
      } else {
        toast.warning(`No ${category} products were migrated`);
      }
      
      // Refresh status after migration
      await checkStatus();
    } catch (error) {
      console.error('Category migration error:', error);
      toast.error(`Failed to migrate ${category} products`);
    } finally {
      setIsLoading(false);
      setCurrentOperation('');
    }
  };

  const clearLocalStorage = async () => {
    if (!migrationStatus?.isAuthenticated) {
      toast.error('Please log in to clear localStorage');
      return;
    }

    setIsLoading(true);
    setCurrentOperation('Clearing localStorage...');
    
    try {
      await clearLocalStorageAfterMigration();
      toast.success('localStorage cleared successfully');
      await checkStatus();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      toast.error('Failed to clear localStorage. Migration may be incomplete.');
    } finally {
      setIsLoading(false);
      setCurrentOperation('');
    }
  };

  const performRollback = async () => {
    if (!migrationStatus?.isAuthenticated) {
      toast.error('Please log in to perform rollback');
      return;
    }

    if (!confirm('Are you sure you want to delete ALL products from Supabase? This cannot be undone.')) {
      return;
    }

    setIsLoading(true);
    setCurrentOperation('Rolling back migration...');
    
    try {
      await rollbackMigration();
      toast.success('Migration rollback completed');
      await checkStatus();
    } catch (error) {
      console.error('Rollback error:', error);
      toast.error('Failed to rollback migration');
    } finally {
      setIsLoading(false);
      setCurrentOperation('');
    }
  };

  const getTotalLocalProducts = () => {
    if (!migrationStatus) return 0;
    return Object.values(migrationStatus.localStorageData).reduce((sum, count) => sum + count, 0);
  };

  const getTotalSupabaseProducts = () => {
    if (!migrationStatus) return 0;
    return Object.values(migrationStatus.supabaseData).reduce((sum, count) => sum + count, 0);
  };

  if (!isAuthenticated) {
    return (
      <MarketplaceLayout>
        <div className="container py-8">
          <Card>
            <CardContent className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
              <p className="text-gray-600 mb-4">
                Please log in to access the Supabase migration tools.
              </p>
            </CardContent>
          </Card>
        </div>
      </MarketplaceLayout>
    );
  }

  return (
    <MarketplaceLayout>
      <div className="container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Supabase Migration</h1>
          <p className="text-gray-600 mb-6">
            Migrate your product data from localStorage to the Supabase backend database with complete account isolation.
          </p>
        </div>

        {/* Current Operation */}
        {isLoading && currentOperation && (
          <Card className="mb-6 border-blue-200 bg-blue-50">
            <CardContent className="py-4">
              <div className="flex items-center gap-3">
                <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
                <span className="text-blue-800 font-medium">{currentOperation}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Migration Status Overview */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Migration Status
              <Button
                variant="outline"
                size="sm"
                onClick={checkStatus}
                disabled={isLoading}
                className="ml-auto"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {migrationStatus ? (
              <div className="space-y-6">
                {/* Data Comparison */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="border-orange-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <HardDrive className="h-5 w-5 text-red-600" />
                        localStorage Data
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {Object.entries(migrationStatus.localStorageData).map(([category, count]) => (
                          <div key={category} className="flex justify-between items-center">
                            <span className="capitalize">{category}:</span>
                            <Badge variant="outline">{count} products</Badge>
                          </div>
                        ))}
                        <div className="border-t pt-3 flex justify-between items-center font-semibold">
                          <span>Total:</span>
                          <Badge variant="secondary">{getTotalLocalProducts()} products</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-green-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Cloud className="h-5 w-5 text-green-600" />
                        Supabase Data
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {Object.entries(migrationStatus.supabaseData).map(([category, count]) => (
                          <div key={category} className="flex justify-between items-center">
                            <span className="capitalize">{category}:</span>
                            <Badge variant="outline">{count} products</Badge>
                          </div>
                        ))}
                        <div className="border-t pt-3 flex justify-between items-center font-semibold">
                          <span>Total:</span>
                          <Badge variant="secondary">{getTotalSupabaseProducts()} products</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Migration Status */}
                {migrationStatus.needsMigration ? (
                  <Alert className="border-orange-200 bg-red-50">
                    <Upload className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-orange-800">
                      Migration needed: You have {getTotalLocalProducts() - getTotalSupabaseProducts()} products in localStorage that haven't been migrated to Supabase.
                    </AlertDescription>
                  </Alert>
                ) : getTotalLocalProducts() > 0 ? (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      All products have been successfully migrated to Supabase!
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert className="border-blue-200 bg-blue-50">
                    <Database className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      No products found in localStorage. You're ready to use the Supabase backend!
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">Loading migration status...</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Migration Actions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Migration Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Full Migration */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-semibold">Full Migration</h3>
                  <p className="text-sm text-gray-600">Migrate all products from all categories</p>
                </div>
                <Button 
                  onClick={runFullMigration}
                  disabled={isLoading || !migrationStatus?.needsMigration}
                  className="gap-2"
                >
                  <Upload className="h-4 w-4" />
                  Migrate All
                </Button>
              </div>

              {/* Category-specific Migration */}
              {migrationStatus && Object.entries(migrationStatus.localStorageData).map(([category, localCount]) => {
                const supabaseCount = migrationStatus.supabaseData[category] || 0;
                const needsMigration = localCount > supabaseCount;
                
                return (
                  <div key={category} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold capitalize">{category} Migration</h3>
                      <p className="text-sm text-gray-600">
                        {localCount} in localStorage, {supabaseCount} in Supabase
                      </p>
                    </div>
                    <Button 
                      variant="outline"
                      onClick={() => runCategoryMigration(category)}
                      disabled={isLoading || !needsMigration}
                      className="gap-2"
                    >
                      <ArrowRight className="h-4 w-4" />
                      Migrate {category}
                    </Button>
                  </div>
                );
              })}

              {/* Clear localStorage */}
              <div className="flex items-center justify-between p-4 border rounded-lg border-orange-200">
                <div>
                  <h3 className="font-semibold">Clear localStorage</h3>
                  <p className="text-sm text-gray-600">Remove localStorage data after successful migration</p>
                </div>
                <Button 
                  variant="outline"
                  onClick={clearLocalStorage}
                  disabled={isLoading || migrationStatus?.needsMigration}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear localStorage
                </Button>
              </div>

              {/* Rollback (Development Only) */}
              <div className="flex items-center justify-between p-4 border rounded-lg border-red-200 bg-red-50">
                <div>
                  <h3 className="font-semibold text-red-800">Rollback Migration</h3>
                  <p className="text-sm text-red-600">⚠️ Delete all products from Supabase (Development only)</p>
                </div>
                <Button 
                  variant="destructive"
                  onClick={performRollback}
                  disabled={isLoading}
                  className="gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Rollback
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Migration Results */}
        {migrationResult && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Migration Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{migrationResult.totalProducts}</div>
                    <div className="text-sm text-gray-600">Total Products</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{migrationResult.totalMigrated}</div>
                    <div className="text-sm text-gray-600">Migrated</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">{migrationResult.totalSkipped}</div>
                    <div className="text-sm text-gray-600">Skipped</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">{migrationResult.totalFailed}</div>
                    <div className="text-sm text-gray-600">Failed</div>
                  </div>
                </div>

                <div className="text-center text-sm text-gray-600">
                  Duration: {(migrationResult.duration / 1000).toFixed(1)} seconds
                </div>

                {/* Category Results */}
                {migrationResult.results.map((result) => (
                  <div key={result.category} className="border rounded-lg p-4">
                    <h4 className="font-semibold capitalize mb-2">{result.category} Category</h4>
                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div>Total: {result.totalProducts}</div>
                      <div>Migrated: {result.migratedProducts}</div>
                      <div>Skipped: {result.skippedProducts}</div>
                      <div>Failed: {result.failedProducts}</div>
                    </div>
                    {result.errors.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-red-600">Errors:</p>
                        <ul className="text-sm text-red-600 list-disc list-inside">
                          {result.errors.slice(0, 3).map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                          {result.errors.length > 3 && (
                            <li>... and {result.errors.length - 3} more errors</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MarketplaceLayout>
  );
}
