import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ArrowLeft,
  Package,
  User,
  MapPin,
  Phone,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  Truck,
  AlertCircle,
  ExternalLink,
  Building2,
  CreditCard,
  Users,
  RefreshCw
} from 'lucide-react';
import {
  getSupplierOrderById,
  updateOrderStatus,
  SupplierOrderWithItems
} from '@/services/supplierOrderService';
import {
  getShippingCompaniesForSupplier,
  assignSupplierOrderToShipping,
  ShippingCompany
} from '@/services/supplierShippingService';
import { supabase } from '@/services/authService';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { motion } from 'framer-motion';

export default function SupplierOrderDetailPage() {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { toast } = useToast();

  // 🚨 CRITICAL DEBUG SECTION - INVESTIGATING BLANK PAGE ISSUE
  console.log('🔍 [CRITICAL_DEBUG] SupplierOrderDetailPage mounted');
  console.log('🔍 [CRITICAL_DEBUG] orderId from params:', orderId);
  console.log('🔍 [CRITICAL_DEBUG] orderId type:', typeof orderId);
  console.log('🔍 [CRITICAL_DEBUG] orderId length:', orderId?.length);
  console.log('🔍 [CRITICAL_DEBUG] Current URL:', window.location.href);
  console.log('🔍 [CRITICAL_DEBUG] Component render timestamp:', new Date().toISOString());

  const [order, setOrder] = useState<SupplierOrderWithItems | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  // Shipping assignment state
  const [shippingCompanies, setShippingCompanies] = useState<ShippingCompany[]>([]);
  const [isShippingDialogOpen, setIsShippingDialogOpen] = useState(false);
  const [selectedShippingCompanyId, setSelectedShippingCompanyId] = useState<string>('');
  const [isAssigningShipping, setIsAssigningShipping] = useState(false);
  const [consumerSelectedCompanyName, setConsumerSelectedCompanyName] = useState<string | null>(null);

  // 🚨 CRITICAL DEBUG - State monitoring
  console.log('🔍 [STATE_DEBUG] Current state:', {
    order: order ? {
      id: order.id,
      order_number: order.order_number,
      status: order.status,
      supplier_items_count: order.supplier_items?.length
    } : null,
    isLoading,
    isUpdating,
    shippingCompaniesCount: shippingCompanies.length,
    selectedShippingCompanyId,
    isAssigningShipping,
    isShippingDialogOpen
  });

  // Load shipping companies
  const loadShippingCompanies = async () => {
    try {
      const result = await getShippingCompaniesForSupplier();
      if (result.success) {
        setShippingCompanies(result.companies || []);
      }
    } catch (error) {
      console.error('❌ [ORDER_DETAIL] Error loading shipping companies:', error);
    }
  };

  // Load consumer selected shipping company name
  const loadConsumerShippingCompanyName = async (companyId: string) => {
    try {
      const { data: company, error } = await supabase
        .from('shipping_companies')
        .select('company_name')
        .eq('id', companyId)
        .single();

      if (error) {
        console.error('❌ [ORDER_DETAIL] Error fetching shipping company name:', error);
        return;
      }

      if (company) {
        setConsumerSelectedCompanyName(company.company_name);
      }
    } catch (error) {
      console.error('❌ [ORDER_DETAIL] Error loading consumer shipping company:', error);
    }
  };

  // Handle shipping assignment
  const handleAssignShipping = async () => {
    if (!order || !selectedShippingCompanyId) return;

    try {
      setIsAssigningShipping(true);
      console.log('🚚 [ORDER_DETAIL] Assigning order to shipping company...', {
        orderId: order.id,
        shippingCompanyId: selectedShippingCompanyId
      });

      const result = await assignSupplierOrderToShipping(order.id, selectedShippingCompanyId);

      console.log('🔍 [ORDER_DETAIL] Assignment result:', result);

      if (result.success) {
        toast({
          title: 'Order Assigned Successfully',
          description: 'Order has been assigned to shipping company and will appear in their dashboard.',
        });

        // Refresh order details
        await loadOrderDetails();

        // Close dialog and reset state
        setIsShippingDialogOpen(false);
        setSelectedShippingCompanyId('');
      } else {
        console.error('❌ [ORDER_DETAIL] Assignment failed:', result.error);
        toast({
          title: 'Assignment Failed',
          description: result.error || 'Failed to assign order to shipping company',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('❌ [ORDER_DETAIL] Error assigning shipping:', error);
      toast({
        title: 'Assignment Failed',
        description: 'Failed to assign order to shipping company',
        variant: 'destructive'
      });
    } finally {
      setIsAssigningShipping(false);
    }
  };

  // Open shipping assignment dialog
  const openShippingDialog = () => {
    setSelectedShippingCompanyId('');
    setIsShippingDialogOpen(true);
  };

  // Load order details
  const loadOrderDetails = async () => {
    if (!orderId) return;

    try {
      setIsLoading(true);
      console.log('🔍 [ORDER_DETAIL] Loading order details for:', orderId);
      console.log('🔍 [ORDER_DETAIL] Order ID type:', typeof orderId);
      console.log('🔍 [ORDER_DETAIL] Order ID length:', orderId.length);

      const result = await getSupplierOrderById(orderId);
      console.log('🔍 [ORDER_DETAIL] Service result:', result);

      if (result.success) {
        console.log('✅ [ORDER_DETAIL] Order loaded successfully:', result.order?.order_number);
        setOrder(result.order || null);

        // Load consumer selected shipping company name if available
        if (result.order?.shipping_company_id) {
          loadConsumerShippingCompanyName(result.order.shipping_company_id);
        }
      } else {
        console.error('❌ [ORDER_DETAIL] Failed to load order:', result.error);
        toast({
          title: 'Failed to load order',
          description: result.error || 'Please try again',
          variant: 'destructive'
        });
        // Don't navigate away immediately - let user see the error
        // navigate('/app/orders');
      }
    } catch (error) {
      console.error('❌ [ORDER_DETAIL] Error loading order:', error);
      toast({
        title: 'Failed to load order',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update order status
  const handleStatusUpdate = async (newStatus: 'confirmed' | 'shipped' | 'cancelled') => {
    if (!order) return;

    try {
      setIsUpdating(true);
      console.log('🔄 [ORDER_DETAIL] Updating status to:', newStatus);

      const result = await updateOrderStatus({
        order_id: order.id,
        new_status: newStatus
      });

      if (result.success) {
        toast({
          title: 'Order status updated',
          description: `Order has been marked as ${newStatus}`,
        });
        // Reload order details
        await loadOrderDetails();
      } else {
        toast({
          title: 'Failed to update status',
          description: result.error || 'Please try again',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('❌ [ORDER_DETAIL] Error updating status:', error);
      toast({
        title: 'Failed to update status',
        description: 'Please try again',
        variant: 'destructive'
      });
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    console.log('🔍 [USEEFFECT] Component mounted/orderId changed:', {
      orderId,
      orderIdType: typeof orderId,
      orderIdLength: orderId?.length,
      timestamp: new Date().toISOString()
    });

    if (orderId) {
      console.log('🔍 [USEEFFECT] Starting data loading...');
      loadOrderDetails();
      loadShippingCompanies();
    } else {
      console.error('🚨 [USEEFFECT] No orderId provided!');
    }
  }, [orderId]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-DZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'confirmed': return 'default';
      case 'shipped': return 'outline';
      case 'delivered': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return Clock;
      case 'confirmed': return CheckCircle;
      case 'shipped': return Truck;
      case 'delivered': return CheckCircle;
      case 'cancelled': return AlertCircle;
      default: return Clock;
    }
  };

  // Debug render states
  console.log('🔍 [RENDER] SupplierOrderDetailPage render state:', {
    isLoading,
    hasOrder: !!order,
    orderId,
    orderData: order ? {
      order_number: order.order_number,
      status: order.status,
      supplier_items_count: order.supplier_items?.length
    } : null
  });

  if (isLoading) {
    console.log('🔍 [RENDER] Showing loading state');
    return (
      <AdminLayout>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => navigate('/app/orders')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Button>
          </div>
          <div className="grid gap-6">
            <div className="bg-blue-100 border border-blue-300 rounded-lg p-4">
              <p className="text-blue-800 font-medium">Loading order details...</p>
              <p className="text-blue-600 text-sm">Order ID: {orderId}</p>
            </div>
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-100 rounded-lg animate-pulse" />
            ))}
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!order) {
    console.log('🔍 [RENDER] Showing order not found state');
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Order not found</h2>
            <p className="text-gray-600 mb-6">The order you're looking for doesn't exist or you don't have access to it.</p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
              <p className="text-red-800 font-medium mb-2">Debug Information:</p>
              <p className="text-red-600 text-sm">Order ID: {orderId}</p>
              <p className="text-red-600 text-sm">Loading completed: {!isLoading ? 'Yes' : 'No'}</p>
              <p className="text-red-600 text-sm">Check browser console for detailed logs</p>
            </div>
            <Button onClick={() => navigate('/app/orders')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const StatusIcon = getStatusIcon(order.status);
  const supplierValue = order.supplier_items.reduce((sum, item) => sum + item.total_price, 0);

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => navigate('/app/orders')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <StatusIcon className="h-8 w-8 text-[#DC2626]" />
                {order.order_number}
              </h1>
              <p className="text-gray-600 mt-1">
                Order placed on {formatDate(order.created_at)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge variant={getStatusBadgeVariant(order.status)} className="text-sm px-3 py-1">
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Badge>
            <Button
              onClick={() => loadOrderDetails()}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Status Update Actions */}
        {order.status === 'pending' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-amber-50 border border-amber-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Clock className="h-6 w-6 text-amber-600" />
                <div>
                  <h3 className="font-semibold text-amber-800">Action Required</h3>
                  <p className="text-sm text-amber-700">This order is pending your confirmation</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => handleStatusUpdate('confirmed')}
                  disabled={isUpdating}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Confirm Order
                </Button>
                <Button
                  onClick={() => handleStatusUpdate('cancelled')}
                  disabled={isUpdating}
                  variant="destructive"
                >
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            </div>
          </motion.div>
        )}

        {order.status === 'confirmed' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-blue-800">Ready for Shipping</h3>
                  <p className="text-sm text-blue-700">Assign to shipping company or mark as shipped when products are picked up</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={openShippingDialog}
                  disabled={isUpdating || isAssigningShipping}
                  className="bg-[#DC2626] hover:bg-[#B91C1C] text-white"
                >
                  <Truck className="h-4 w-4 mr-2" />
                  Assign Shipping
                </Button>
                <Button
                  onClick={() => handleStatusUpdate('shipped')}
                  disabled={isUpdating}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Mark as Shipped
                </Button>
              </div>
            </div>
          </motion.div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Order Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-[#DC2626] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-600">Name</span>
                      <p className="font-medium text-gray-900">
                        {order.consumer_name || 'Consumer'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-[#DC2626]" />
                    <div>
                      <span className="text-sm text-gray-600">Phone</span>
                      <p className="font-medium text-gray-900">{order.consumer_phone}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Delivery Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Delivery Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-[#DC2626] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-600">Wilaya</span>
                      <p className="font-medium text-gray-900">{order.delivery_wilaya}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-4 w-4 text-[#DC2626]" />
                    <div>
                      <span className="text-sm text-gray-600">Payment Method</span>
                      <p className="font-medium text-gray-900">
                        {order.payment_method === 'cash_on_delivery' ? 'Cash on Delivery' : 'Store Pickup'}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-[#DC2626] rounded-full mt-2"></div>
                  <div className="flex-1">
                    <span className="text-sm text-gray-600">Address</span>
                    <p className="font-medium text-gray-900 mt-1">{order.delivery_address}</p>
                  </div>
                </div>
                {order.special_instructions && (
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-[#DC2626] rounded-full mt-2"></div>
                    <div className="flex-1">
                      <span className="text-sm text-gray-600">Special Instructions</span>
                      <p className="font-medium text-gray-900 mt-1">{order.special_instructions}</p>
                    </div>
                  </div>
                )}
                <div className="pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(order.google_maps_url, '_blank')}
                    className="text-[#DC2626] border-[#DC2626] hover:bg-[#DC2626] hover:text-white"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View on Google Maps
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Information - Consumer's Choice */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Consumer's Shipping Choice
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Shipping company selected by consumer during checkout
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {order.shipping_company_id ? (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div>
                        <span className="text-sm text-gray-600">Selected Company</span>
                        <p className="font-medium text-gray-900">
                          {consumerSelectedCompanyName || 'Loading company name...'}
                        </p>
                        {consumerSelectedCompanyName && (
                          <p className="text-xs text-gray-500">ID: {order.shipping_company_id}</p>
                        )}
                      </div>
                    </div>
                    {order.shipping_cost && order.shipping_cost > 0 && (
                      <div className="flex items-center gap-3 mb-3">
                        <DollarSign className="h-4 w-4 text-blue-500" />
                        <div>
                          <span className="text-sm text-gray-600">Shipping Cost</span>
                          <p className="font-medium text-gray-900">
                            {order.shipping_cost.toLocaleString()} DA
                          </p>
                        </div>
                      </div>
                    )}
                    {order.shipping_method && (
                      <div className="flex items-center gap-3">
                        <Package className="h-4 w-4 text-blue-500" />
                        <div>
                          <span className="text-sm text-gray-600">Shipping Method</span>
                          <p className="font-medium text-gray-900 capitalize">
                            {order.shipping_method}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                    <Truck className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600 font-medium">No shipping company selected</p>
                    <p className="text-gray-500 text-sm">
                      Consumer did not select a shipping company during checkout
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Your Products */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Your Products ({order.supplier_items.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.supplier_items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                      <img
                        src={item.product_image || '/placeholder.svg'}
                        alt={item.product_name}
                        className="w-16 h-16 object-cover rounded-md"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">
                          {item.product_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {item.quantity} × {formatCurrency(item.unit_price)}
                        </p>
                        <p className="text-xs text-gray-400">
                          {item.category} • {item.marketplace_section}
                        </p>
                      </div>
                      <p className="font-semibold text-lg">
                        {formatCurrency(item.total_price)}
                      </p>
                    </div>
                  ))}
                </div>

                <Separator className="my-4" />

                <div className="flex justify-between items-center">
                  <span className="font-semibold text-lg">Your Total:</span>
                  <span className="font-bold text-xl text-[#DC2626]">
                    {formatCurrency(supplierValue)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{formatCurrency(order.subtotal)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span>{formatCurrency(order.total_shipping_cost)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>AROUZ Fees</span>
                  <span>{formatCurrency(order.total_arouz_fees)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>{formatCurrency(order.total_amount)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-[#DC2626]">
                  <span>Your Revenue</span>
                  <span>{formatCurrency(supplierValue)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Other Suppliers */}
            {order.other_suppliers.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Other Suppliers ({order.other_suppliers.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {order.other_suppliers.map((supplier, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Building2 className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="font-medium text-sm">{supplier.supplier_name}</p>
                            <p className="text-xs text-gray-500">{supplier.supplier_wilaya}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{formatCurrency(supplier.total_value)}</p>
                          <p className="text-xs text-gray-500">{supplier.item_count} items</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Order Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Order Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-[#DC2626] rounded-full"></div>
                    <div>
                      <p className="font-medium text-sm">Order Placed</p>
                      <p className="text-xs text-gray-500">{formatDate(order.created_at)}</p>
                    </div>
                  </div>
                  {order.confirmed_at && (
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">Order Confirmed</p>
                        <p className="text-xs text-gray-500">{formatDate(order.confirmed_at)}</p>
                      </div>
                    </div>
                  )}
                  {order.delivered_at && (
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">Order Delivered</p>
                        <p className="text-xs text-gray-500">{formatDate(order.delivered_at)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Shipping Assignment Dialog */}
        <Dialog open={isShippingDialogOpen} onOpenChange={setIsShippingDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Assign Order to Shipping Company</DialogTitle>
              <DialogDescription>
                Select a shipping company to handle the delivery of this order.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <label htmlFor="shipping-company" className="text-sm font-medium">
                  Shipping Company
                </label>
                <Select
                  value={selectedShippingCompanyId}
                  onValueChange={setSelectedShippingCompanyId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a shipping company" />
                  </SelectTrigger>
                  <SelectContent>
                    {shippingCompanies.map((company) => (
                      <SelectItem key={company.id} value={company.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{company.company_name}</span>
                          <span className="text-xs text-gray-500">
                            Coverage: {company.coverage_areas.slice(0, 3).join(', ')}
                            {company.coverage_areas.length > 3 && ` +${company.coverage_areas.length - 3} more`}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsShippingDialogOpen(false)}
                disabled={isAssigningShipping}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAssignShipping}
                disabled={!selectedShippingCompanyId || isAssigningShipping}
                className="bg-[#DC2626] hover:bg-[#B91C1C]"
              >
                {isAssigningShipping ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Assigning...
                  </>
                ) : (
                  <>
                    <Truck className="h-4 w-4 mr-2" />
                    Assign Order
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminLayout>
  );
}
