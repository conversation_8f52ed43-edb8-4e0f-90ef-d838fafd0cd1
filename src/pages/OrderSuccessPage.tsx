import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import {
  CheckCircle,
  Package,
  Truck,
  Phone,
  MapPin,
  Calendar,
  ArrowRight,
  Home,
  ShoppingBag,
  User,
  Building2,
  ExternalLink
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { getOrderById, OrderWithItems } from '@/services/orderService';
import { useAuth } from '@/contexts/AuthContext';
import { getFormattedWilaya } from '@/data/algeria-cities';

export function OrderSuccessPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated } = useAuth();
  
  const [order, setOrder] = useState<OrderWithItems | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/');
      return;
    }

    if (!orderId) {
      setError('Order ID not found');
      setIsLoading(false);
      return;
    }

    const fetchOrder = async () => {
      try {
        console.log('🔍 [ORDER_SUCCESS] Fetching order with ID:', orderId);
        const result = await getOrderById(orderId);
        console.log('🔍 [ORDER_SUCCESS] Order fetch result:', result);

        if (result.success && result.order) {
          console.log('🔍 [ORDER_SUCCESS] Order data:', result.order);
          console.log('🔍 [ORDER_SUCCESS] Order items count:', result.order.order_items?.length || 0);
          console.log('🔍 [ORDER_SUCCESS] Order items:', result.order.order_items);
          setOrder(result.order);
        } else {
          console.error('❌ [ORDER_SUCCESS] Failed to load order:', result.error);
          setError(result.error || 'Failed to load order');
        }
      } catch (err) {
        console.error('❌ [ORDER_SUCCESS] Exception:', err);
        setError('An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrder();
  }, [orderId, isAuthenticated, navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#DC2626] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Package className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {t('orderSuccess.orderNotFound')}
          </h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button onClick={() => navigate('/')} className="bg-[#DC2626] hover:bg-[#DC2626]/90">
            {t('common.backToHome')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
              className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <CheckCircle className="h-10 w-10 text-green-600" />
            </motion.div>
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-3xl font-bold text-gray-900 mb-2"
            >
              {t('orderSuccess.title')}
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-gray-600"
            >
              {t('orderSuccess.description')}
            </motion.p>
          </div>
        </div>
      </div>

      {/* Order Details */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Order Summary Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-6 w-6 text-[#DC2626]" />
                  <span>{t('orderSuccess.orderDetails')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">{t('orderSuccess.orderNumber')}</p>
                    <p className="font-semibold text-lg">{order.order_number}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">{t('orderSuccess.orderDate')}</p>
                    <p className="font-semibold">
                      {new Date(order.created_at).toLocaleDateString('fr-DZ', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">{t('orderSuccess.status')}</p>
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      {t(`orderStatus.${order.status}`)}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">{t('orderSuccess.paymentMethod')}</p>
                    <div className="flex items-center space-x-2">
                      {order.payment_method === 'cash_on_delivery' ? (
                        <Truck className="h-4 w-4 text-gray-600" />
                      ) : (
                        <ShoppingBag className="h-4 w-4 text-gray-600" />
                      )}
                      <span className="font-semibold">
                        {order.payment_method === 'cash_on_delivery' 
                          ? t('checkout.cashOnDelivery')
                          : t('checkout.storePickup')
                        }
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Customer Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-6 w-6 text-[#DC2626]" />
                  <span>{t('orderSuccess.customerInfo')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[#DC2626] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-600">{t('orderSuccess.name')}</span>
                      <p className="font-medium text-gray-900">{order.consumer_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-[#DC2626]" />
                    <div>
                      <span className="text-sm text-gray-600">{t('orderSuccess.phone')}</span>
                      <p className="font-medium text-gray-900">{order.consumer_phone}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Delivery Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-6 w-6 text-[#DC2626]" />
                  <span>{t('orderSuccess.deliveryInfo')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-[#DC2626] rounded-full"></div>
                      <div>
                        <span className="text-sm text-gray-600">{t('orderSuccess.wilaya')}</span>
                        <p className="font-medium text-gray-900">{order.delivery_wilaya}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-[#DC2626] rounded-full mt-2"></div>
                    <div className="flex-1">
                      <span className="text-sm text-gray-600">{t('orderSuccess.deliveryAddress')}</span>
                      <p className="font-medium text-gray-900 mt-1 leading-relaxed">
                        {order.delivery_address}
                      </p>
                    </div>
                  </div>
                  {order.special_instructions && (
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-[#DC2626] rounded-full mt-2"></div>
                      <div className="flex-1">
                        <span className="text-sm text-gray-600">{t('orderSuccess.specialInstructions')}</span>
                        <p className="font-medium text-gray-900 mt-1 leading-relaxed">
                          {order.special_instructions}
                        </p>
                      </div>
                    </div>
                  )}
                  {/* CRITICAL: View on Maps button - EXACT MATCH with Order Confirmation Step */}
                  <div className="pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(order.google_maps_url, '_blank')}
                      className="text-[#DC2626] border-[#DC2626] hover:bg-[#DC2626] hover:text-white transition-colors"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      {t('orderSuccess.viewOnMaps')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>



          {/* Order Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            {/* Order Summary - EXACT MATCH with Order Confirmation Step */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
                <Package className="h-5 w-5 mr-2" />
                {t('checkout.step4.orderSummary')}
              </h3>

              {!order.order_items || order.order_items.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No order items found</p>
                </div>
              ) : (
                <>
                  {/* Items - EXACT MATCH with Order Summary structure */}
                  <div className="space-y-3 mb-4">
                    {order.order_items.map((item, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <img
                          src={item.product_image || '/placeholder.svg'}
                          alt={item.product_name}
                          className="w-12 h-12 object-cover rounded-md"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {item.product_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {item.quantity} × {new Intl.NumberFormat('fr-DZ', {
                              style: 'currency',
                              currency: 'DZD',
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0
                            }).format(item.unit_price)}
                          </p>
                        </div>
                        <p className="text-sm font-medium">
                          {new Intl.NumberFormat('fr-DZ', {
                            style: 'currency',
                            currency: 'DZD',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          }).format(item.total_price)}
                        </p>
                      </div>
                    ))}
                  </div>
                </>
              )}

                <Separator />

                {/* Pricing Breakdown - Real shipping and AROUZ fees */}
                <div className="space-y-2 mt-4">
                  <div className="flex justify-between text-sm">
                    <span>{t('orderSuccess.subtotal')}</span>
                    <span>{new Intl.NumberFormat('fr-DZ', {
                      style: 'currency',
                      currency: 'DZD',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(order.subtotal)}</span>
                  </div>

                  {/* Shipping Cost */}
                  <div className="flex justify-between text-sm">
                    <span>{t('orderSuccess.shipping')}</span>
                    <span>
                      {(order.shipping_cost || 0) > 0 ? (
                        new Intl.NumberFormat('fr-DZ', {
                          style: 'currency',
                          currency: 'DZD',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0
                        }).format(order.shipping_cost || 0)
                      ) : (
                        <span className="text-green-600">Free</span>
                      )}
                    </span>
                  </div>

                  {/* AROUZ Fees */}
                  <div className="flex justify-between text-sm">
                    <span>{t('orderSuccess.arouzFee')}</span>
                    <span>
                      {(order.total_arouz_fees || 0) > 0 ? (
                        new Intl.NumberFormat('fr-DZ', {
                          style: 'currency',
                          currency: 'DZD',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0
                        }).format(order.total_arouz_fees || 0)
                      ) : (
                        <span className="text-green-600">Free</span>
                      )}
                    </span>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="flex justify-between font-bold text-lg">
                  <span>{t('orderSuccess.total')}</span>
                  <span className="text-[#DC2626]">
                    {new Intl.NumberFormat('fr-DZ', {
                      style: 'currency',
                      currency: 'DZD',
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    }).format(order.total_amount)}
                  </span>
                </div>
            </div>
          </motion.div>

          {/* Shipping Origins */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            {/* Shipping Origins - EXACT MATCH with Order Confirmation Step */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center text-lg">
                <Building2 className="h-5 w-5 mr-3 text-[#DC2626]" />
                {t('checkout.step4.shippingOrigins')}
              </h3>
              {!order.order_items || order.order_items.length === 0 ? (
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No shipping origins found</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {order.order_items.map((item, index) => {
                    const formattedWilaya = getFormattedWilaya(item.supplier_wilaya) || item.supplier_wilaya || 'Location TBD';
                    return (
                      <div key={`${item.id}-${index}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <img
                            src={item.product_image || '/placeholder.svg'}
                            alt={item.product_name}
                            className="w-10 h-10 object-cover rounded-md"
                          />
                          <div>
                            <p className="font-medium text-gray-900 text-sm">{item.product_name}</p>
                            <p className="text-xs text-gray-600">
                              Ships from: {formattedWilaya}
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="border-gray-300 text-gray-600 text-xs">
                          {formattedWilaya}
                        </Badge>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </motion.div>

          {/* Next Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-blue-900">
                  <Calendar className="h-6 w-6" />
                  <span>{t('orderSuccess.nextSteps')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="text-blue-800">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold">1</span>
                    </div>
                    <p className="text-sm">{t('orderSuccess.step1')}</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold">2</span>
                    </div>
                    <p className="text-sm">{t('orderSuccess.step2')}</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-bold">3</span>
                    </div>
                    <p className="text-sm">{t('orderSuccess.step3')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button
              onClick={() => navigate('/')}
              variant="outline"
              size="lg"
              className="flex items-center space-x-2"
            >
              <Home className="h-5 w-5" />
              <span>{t('orderSuccess.backToHome')}</span>
            </Button>
            <Button
              onClick={() => navigate('/my-orders')}
              size="lg"
              className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white flex items-center space-x-2"
            >
              <Package className="h-5 w-5" />
              <span>{t('orderSuccess.viewOrders')}</span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
