import React from 'react';
import { AdminPanelLayout } from '@/components/layout/AdminPanelLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3, Construction } from 'lucide-react';

export default function AdminAnalyticsPage() {
  return (
    <AdminPanelLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics & Reports</h1>
          <p className="text-gray-600 mt-1">
            Performance insights and operational analytics
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Construction className="h-5 w-5 text-[#DC2626]" />
              Coming Soon
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Analytics Dashboard
              </h3>
              <p className="text-gray-600">
                This section will provide comprehensive analytics including order volumes, 
                revenue trends, shipping performance, and operational insights.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminPanelLayout>
  );
}
