import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLocation } from '@/contexts/LocationContext';
import { 
  searchAllAdministrativeLocations, 
  initializeCompleteData, 
  isCompleteDataLoaded,
  getCurrentWilayasData,
  SearchResult 
} from '@/data/algeria-cities';
import { MapPin, Search, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import AccurateLocationTest from '@/components/location/AccurateLocationTest';
import PreciseLocationTest from '@/components/location/PreciseLocationTest';

/**
 * Location System Test Page
 * 
 * This page demonstrates and tests the enhanced Algerian location system:
 * 1. Complete 58 wilayas with daïras and communes
 * 2. Enhanced search functionality across all administrative levels
 * 3. Automatic GPS detection on first visit
 * 4. Improved address formatting with proper hierarchy
 */
export default function LocationSystemTestPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isDataLoading, setIsDataLoading] = useState(false);
  const [dataLoadStatus, setDataLoadStatus] = useState<'loading' | 'loaded' | 'error'>('loading');
  
  const {
    selectedLocation,
    isLocationLoading,
    locationError,
    hasLocationPermission,
    isFirstVisit,
    autoLocationAttempted,
    getLocationString,
    requestCurrentLocation,
    clearLocation,
    attemptAutoLocation
  } = useLocation();

  // Initialize complete data on component mount
  useEffect(() => {
    const loadData = async () => {
      setIsDataLoading(true);
      try {
        await initializeCompleteData();
        setDataLoadStatus('loaded');
      } catch (error) {
        console.error('Failed to load complete data:', error);
        setDataLoadStatus('error');
      } finally {
        setIsDataLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      const results = searchAllAdministrativeLocations(query, 15);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  };

  // Handle location selection from search results
  const handleLocationSelect = (result: SearchResult) => {
    const location = result.location;
    if (location.coordinates) {
      // This would normally be handled by the location context
      console.log('Selected location:', result);
    }
  };

  const wilayasData = getCurrentWilayasData();
  const isCompleteDataReady = isCompleteDataLoaded();

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Enhanced Algerian Location System Test
        </h1>
        <p className="text-gray-600">
          Testing complete administrative divisions and automatic GPS detection
        </p>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              {isCompleteDataReady ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
              )}
              <span className="text-sm">
                Complete Data: {isCompleteDataReady ? 'Loaded' : 'Loading...'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              {hasLocationPermission ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm">
                GPS Permission: {hasLocationPermission ? 'Granted' : 'Not Granted'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={isFirstVisit ? "default" : "secondary"}>
                {isFirstVisit ? 'First Visit' : 'Returning User'}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={autoLocationAttempted ? "default" : "secondary"}>
                Auto Location: {autoLocationAttempted ? 'Attempted' : 'Pending'}
              </Badge>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            <strong>Wilayas Loaded:</strong> {wilayasData.length} / 58
          </div>
        </CardContent>
      </Card>

      {/* Current Location */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-red-600" />
            Current Location
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {selectedLocation ? (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="font-medium text-green-900">
                {getLocationString()}
              </div>
              <div className="text-sm text-green-700 mt-1">
                Coordinates: {selectedLocation.coordinates.lat.toFixed(4)}, {selectedLocation.coordinates.lng.toFixed(4)}
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="text-gray-600">No location selected</div>
            </div>
          )}
          
          {locationError && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="text-red-700">{locationError}</div>
            </div>
          )}
          
          <div className="flex gap-2">
            <Button 
              onClick={requestCurrentLocation}
              disabled={isLocationLoading}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isLocationLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <MapPin className="h-4 w-4 mr-2" />
              )}
              Get Current Location
            </Button>
            
            <Button 
              onClick={attemptAutoLocation}
              variant="outline"
              disabled={isLocationLoading}
            >
              Test Auto Location
            </Button>
            
            <Button 
              onClick={clearLocation}
              variant="outline"
            >
              Clear Location
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Enhanced Administrative Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search wilayas, daïras, or communes (e.g., 'Alger', 'بجاية', 'Constantine')"
              className="pl-9"
            />
          </div>
          
          {searchResults.length > 0 && (
            <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
              {searchResults.map((result, index) => (
                <button
                  key={`${result.type}-${result.location.code}-${index}`}
                  onClick={() => handleLocationSelect(result)}
                  className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {result.location.name}
                      </div>
                      <div className="text-sm text-gray-600">
                        {result.location.name_ar}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {result.hierarchy}
                      </div>
                    </div>
                    <Badge 
                      variant={
                        result.type === 'wilaya' ? 'default' :
                        result.type === 'daira' ? 'secondary' : 'outline'
                      }
                      className="ml-2"
                    >
                      {result.type}
                    </Badge>
                  </div>
                </button>
              ))}
            </div>
          )}
          
          {searchQuery && searchResults.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No results found for "{searchQuery}"
            </div>
          )}
        </CardContent>
      </Card>

      {/* 100% Precise Location Detection Test (Polygon-Based) */}
      <PreciseLocationTest />

      {/* Accurate Location Detection Test (Boundary-Based) */}
      <AccurateLocationTest />

      {/* Data Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Data Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{wilayasData.length}</div>
              <div className="text-sm text-blue-700">Wilayas</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {wilayasData.reduce((total, wilaya) => total + wilaya.dairas.length, 0)}
              </div>
              <div className="text-sm text-green-700">Daïras</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {wilayasData.reduce((total, wilaya) =>
                  total + wilaya.dairas.reduce((dTotal, daira) => dTotal + daira.communes.length, 0), 0
                )}
              </div>
              <div className="text-sm text-purple-700">Communes</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
