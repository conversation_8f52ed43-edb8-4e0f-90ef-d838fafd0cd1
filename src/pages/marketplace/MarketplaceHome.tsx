import React from 'react';
import { useTranslation } from 'react-i18next';
import { MarketplaceLayout } from '@/components/layout/MarketplaceLayout';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  Car, 
  ShoppingBag, 
  Search, 
  Truck, 
  Shield, 
  Clock, 
  ArrowRight 
} from 'lucide-react';

export default function MarketplaceHome() {
  const { t } = useTranslation();
  
  return (
    <MarketplaceLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#071c44] to-[#0a2a66] text-white py-20">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {t('marketplace.heroTitle')}
              </h1>
              <p className="text-xl mb-8 text-white/80">
                {t('marketplace.heroSubtitle')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  asChild
                  size="lg"
                  className="bg-[#DC2626] hover:bg-[#B91C1C] text-white"
                >
                  <Link to="/my-vehicle-parts">
                    <Car className="mr-2 h-5 w-5" />
                    {t('marketplace.findPartsForVehicle')}
                  </Link>
                </Button>
                <Button 
                  asChild
                  size="lg" 
                  variant="outline" 
                  className="bg-white text-[#071c44] hover:bg-white/90 border-white"
                >
                  <Link to="/wholesale-offers">
                    <ShoppingBag className="mr-2 h-5 w-5" />
                    {t('marketplace.browseWholesale')}
                  </Link>
                </Button>
              </div>
            </div>
            <div className="hidden md:block">
              <img
                src="/images/HeroSection.png"
                alt="KALIX"
                className="w-full h-auto rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Main Categories Section */}
      <section className="py-16 bg-white">
        <div className="container">
          <h2 className="text-3xl font-bold text-center mb-12">
            {t('marketplace.mainCategories')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Vehicle Parts Category */}
            <div className="bg-[#ffd9a3]/30 rounded-2xl p-8 transition-transform hover:scale-[1.02]">
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-[#fa7b00] rounded-xl p-3 text-white">
                  <Car className="h-8 w-8" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold mb-2">{t('marketplace.myVehicleParts')}</h3>
                  <p className="text-muted-foreground">
                    {t('marketplace.vehiclePartsDescription')}
                  </p>
                </div>
              </div>
              <Button 
                asChild
                className="w-full bg-[#fa7b00] hover:bg-[#fa7b00]/90 text-white"
              >
                <Link to="/my-vehicle-parts">
                  {t('marketplace.findParts')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
            
            {/* Wholesale Offers Category */}
            <div className="bg-[#071c44]/10 rounded-2xl p-8 transition-transform hover:scale-[1.02]">
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-[#071c44] rounded-xl p-3 text-white">
                  <ShoppingBag className="h-8 w-8" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold mb-2">{t('marketplace.wholesaleOffers')}</h3>
                  <p className="text-muted-foreground">
                    {t('marketplace.wholesaleDescription')}
                  </p>
                </div>
              </div>
              <Button 
                asChild
                className="w-full bg-[#071c44] hover:bg-[#071c44]/90 text-white"
              >
                <Link to="/wholesale-offers">
                  {t('marketplace.browseOffers')}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="container">
          <h2 className="text-3xl font-bold text-center mb-4">
            {t('marketplace.whyChooseUs')}
          </h2>
          <p className="text-center text-muted-foreground max-w-2xl mx-auto mb-12">
            {t('marketplace.whyChooseUsDescription')}
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="bg-[#fa7b00]/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Search className="h-6 w-6 text-[#fa7b00]" />
              </div>
              <h3 className="text-lg font-semibold mb-2">{t('marketplace.easySearch')}</h3>
              <p className="text-muted-foreground">
                {t('marketplace.easySearchDescription')}
              </p>
            </div>
            
            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="bg-[#fa7b00]/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Truck className="h-6 w-6 text-[#fa7b00]" />
              </div>
              <h3 className="text-lg font-semibold mb-2">{t('marketplace.fastDelivery')}</h3>
              <p className="text-muted-foreground">
                {t('marketplace.fastDeliveryDescription')}
              </p>
            </div>
            
            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="bg-[#fa7b00]/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-[#fa7b00]" />
              </div>
              <h3 className="text-lg font-semibold mb-2">{t('marketplace.qualityGuarantee')}</h3>
              <p className="text-muted-foreground">
                {t('marketplace.qualityGuaranteeDescription')}
              </p>
            </div>
            
            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="bg-[#fa7b00]/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-[#fa7b00]" />
              </div>
              <h3 className="text-lg font-semibold mb-2">{t('marketplace.support')}</h3>
              <p className="text-muted-foreground">
                {t('marketplace.supportDescription')}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[#071c44] text-white">
        <div className="container text-center">
          <h2 className="text-3xl font-bold mb-4">
            {t('marketplace.readyToShop')}
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto mb-8">
            {t('marketplace.readyToShopDescription')}
          </p>
          <Button
            asChild
            size="lg"
            className="bg-[#DC2626] hover:bg-[#B91C1C] text-white"
          >
            <Link to="/my-vehicle-parts">
              {t('marketplace.startShopping')}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>
    </MarketplaceLayout>
  );
}
