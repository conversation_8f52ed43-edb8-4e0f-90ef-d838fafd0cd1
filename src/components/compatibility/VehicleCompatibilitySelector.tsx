import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Car, X, Plus, Search, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import VehicleTypeSelector from './VehicleTypeSelector';
import EnhancedDropdown from '@/components/ui/enhanced-dropdown';
import {
  fetchCarBrands,
  fetchCarModels,
  fetchCarEngines,
  getVehicleByIdentifiers,
  parseUniqueId
} from '@/services/vehicleDataService';

export interface VehicleCompatibility {
  id: string;
  type: string;
  brand: string;
  model: string;
  generation?: string;
  engineType?: string;
  engineDetails?: {
    code?: string;
    fuel_type?: string;
    power_kW?: number;
    power_HP?: number;
    fullName?: string;
  };
  displayName: string;
}

interface VehicleCompatibilitySelectorProps {
  value: VehicleCompatibility[];
  onChange: (vehicles: VehicleCompatibility[]) => void;
  isOpen: boolean;
  onClose: () => void;
}

const VehicleCompatibilitySelector: React.FC<VehicleCompatibilitySelectorProps> = ({
  value = [],
  onChange,
  isOpen,
  onClose
}) => {
  const [selectedVehicles, setSelectedVehicles] = useState<VehicleCompatibility[]>(value);
  const [selectedType, setSelectedType] = useState('car');
  const [filters, setFilters] = useState({
    brand: '',
    model: '',
    engineType: ''
  });
  const [loading, setLoading] = useState(false);
  const [brandOptions, setBrandOptions] = useState<any[]>([]);
  const [modelOptions, setModelOptions] = useState<any[]>([]);
  const [engineOptions, setEngineOptions] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch brands when component mounts or vehicle type changes
  useEffect(() => {
    const getBrands = async () => {
      setLoading(true);
      try {
        if (selectedType === 'car') {
          const brands = await fetchCarBrands();

          if (!brands || brands.length === 0) {
            console.error('No brands fetched from the database');
            // You can add a toast notification here if you have a toast component
            setBrandOptions([]);
          } else {
            console.log(`Successfully loaded ${brands.length} vehicle brands`);
            setBrandOptions(brands);
          }
        } else {
          // For other vehicle types
          setBrandOptions([]);
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
        setBrandOptions([]);
      } finally {
        setLoading(false);
      }
    };

    getBrands();

    // Reset filters when changing vehicle type
    setFilters({
      brand: '',
      model: '',
      engineType: ''
    });
  }, [selectedType]);

  // Fetch models when brand changes
  useEffect(() => {
    const getModels = async () => {
      if (!filters.brand) {
        setModelOptions([]);
        return;
      }

      setLoading(true);
      try {
        if (selectedType === 'car') {
          const models = await fetchCarModels(filters.brand);

          if (!models || models.length === 0) {
            console.warn(`No models found for brand: ${filters.brand}`);
            setModelOptions([]);
          } else {
            console.log(`Successfully loaded ${models.length} models for brand: ${filters.brand}`);
            setModelOptions(models);
          }
        } else {
          // For other vehicle types
          setModelOptions([]);
        }
      } catch (error) {
        console.error('Error fetching models:', error);
        setModelOptions([]);
      } finally {
        setLoading(false);
      }
    };

    getModels();
  }, [selectedType, filters.brand]);

  // Fetch engines when model changes
  useEffect(() => {
    const getEngines = async () => {
      if (!filters.brand || !filters.model) {
        setEngineOptions([]);
        return;
      }

      setLoading(true);
      try {
        if (selectedType === 'car') {
          // Use the uniqueId directly from the model filter
          const uniqueId = filters.model;

          // Parse the uniqueId to get model and generation information
          const { model: modelName, generationCode } = parseUniqueId(uniqueId);

          console.log(`Fetching engines for ${filters.brand} ${modelName} ${generationCode || ''}`);

          const engines = await fetchCarEngines(filters.brand, modelName, generationCode);

          if (!engines || engines.length === 0) {
            console.warn(`No engines found for ${filters.brand} ${modelName} ${generationCode || ''}`);
            setEngineOptions([]);
          } else {
            console.log(`Successfully loaded ${engines.length} engines for ${filters.brand} ${modelName} ${generationCode || ''}`);
            setEngineOptions(engines);
          }
        } else {
          // For other vehicle types
          setEngineOptions([]);
        }
      } catch (error) {
        console.error('Error fetching engines:', error);
        setEngineOptions([]);
      } finally {
        setLoading(false);
      }
    };

    getEngines();
  }, [selectedType, filters.brand, filters.model]);

  const handleFilterChange = (filterName: string, value: string) => {
    // Reset dependent filters
    if (filterName === 'brand') {
      setFilters({
        brand: value,
        model: '',
        engineType: ''
      });
    } else if (filterName === 'model') {
      setFilters({
        ...filters,
        model: value,
        engineType: ''
      });
    } else {
      setFilters({
        ...filters,
        [filterName]: value
      });
    }
  };

  const handleTypeChange = (type: string) => {
    // Don't do anything if the same type is selected
    if (type === selectedType) return;

    // Reset filters when changing vehicle type
    setFilters({
      brand: '',
      model: '',
      engineType: ''
    });

    // Change the selected type
    setSelectedType(type);
  };

  const handleClearFilters = () => {
    setFilters({
      brand: '',
      model: '',
      engineType: ''
    });
  };

  const handleAddVehicle = async () => {
    if (!filters.brand) return;

    setLoading(true);
    try {
      // If all filters are selected, get the specific vehicle
      if (filters.brand && filters.model && filters.engineType) {
        console.log(`Adding vehicle: ${filters.brand} ${filters.model} ${filters.engineType}`);

        const vehicle = await getVehicleByIdentifiers(
          filters.brand,
          filters.model,
          filters.engineType
        );

        if (vehicle) {
          // Check if this vehicle is already selected
          const isDuplicate = selectedVehicles.some(v => v.id === vehicle.id);
          if (isDuplicate) {
            console.log(`Vehicle already selected: ${vehicle.displayName}`);
          } else {
            const newVehicle: VehicleCompatibility = {
              ...vehicle,
              displayName: `${vehicle.brand} ${vehicle.model} ${vehicle.generation || ''} ${vehicle.engineDetails?.fullName || ''}`
            };
            console.log(`Adding new vehicle: ${newVehicle.displayName}`);
            setSelectedVehicles(prev => [...prev, newVehicle]);
          }
        } else {
          console.error(`Failed to get vehicle data for ${filters.brand} ${filters.model} ${filters.engineType}`);
        }
      }
      // If only brand and model are selected, add the model without specific engine
      else if (filters.brand && filters.model) {
        const { model, generationCode } = parseUniqueId(filters.model);
        const modelOption = modelOptions.find(m => m.uniqueId === filters.model);

        if (modelOption) {
          const vehicleId = `${filters.brand}-${model}-${generationCode}`.replace(/\s+/g, '-').toLowerCase();

          // Check if this vehicle is already selected
          const isDuplicate = selectedVehicles.some(v => v.id === vehicleId);
          if (isDuplicate) {
            console.log(`Vehicle already selected: ${filters.brand} ${model} ${generationCode || ''}`);
          } else {
            const newVehicle: VehicleCompatibility = {
              id: vehicleId,
              type: selectedType,
              brand: filters.brand,
              model: model,
              generation: generationCode,
              displayName: `${filters.brand} ${model} ${generationCode || ''}`
            };
            console.log(`Adding new vehicle: ${newVehicle.displayName}`);
            setSelectedVehicles(prev => [...prev, newVehicle]);
          }
        } else {
          console.error(`Model option not found for ${filters.model}`);
        }
      }
      // If only brand is selected, add the brand without specific model
      else if (filters.brand) {
        const vehicleId = `${filters.brand}`.replace(/\s+/g, '-').toLowerCase();

        // Check if this vehicle is already selected
        const isDuplicate = selectedVehicles.some(v => v.id === vehicleId);
        if (isDuplicate) {
          console.log(`Brand already selected: ${filters.brand}`);
        } else {
          const newVehicle: VehicleCompatibility = {
            id: vehicleId,
            type: selectedType,
            brand: filters.brand,
            model: '',
            displayName: filters.brand
          };
          console.log(`Adding new brand: ${newVehicle.displayName}`);
          setSelectedVehicles(prev => [...prev, newVehicle]);
        }
      }
    } catch (error) {
      console.error('Error adding vehicle:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveVehicle = (id: string) => {
    setSelectedVehicles(prev => prev.filter(v => v.id !== id));
  };

  const handleSave = () => {
    onChange(selectedVehicles);
    onClose();
  };

  const handleCancel = () => {
    setSelectedVehicles(value);
    onClose();
  };

  // Filter selected vehicles based on search query
  const filteredVehicles = selectedVehicles.filter(vehicle =>
    vehicle.displayName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Render brand option
  const renderBrandOption = (option: any) => (
    <div className="flex items-center gap-2">
      <div className="flex-shrink-0 w-7 h-7 rounded-md bg-gray-100 flex items-center justify-center">
        {option.image_url ? (
          <img src={option.image_url} alt={option.label} className="w-5 h-5 object-contain" />
        ) : (
          <span className="text-sm font-medium text-gray-500">
            {option.label.charAt(0)}
          </span>
        )}
      </div>
      <span className="font-medium">{option.label}</span>
    </div>
  );

  // Render model option
  const renderModelOption = (option: any) => (
    <div className="flex flex-col py-0.5">
      <div className="flex items-center gap-1">
        <span className="font-medium">{option.model}</span>
        <span className="text-xs bg-gray-100 px-1 rounded">{option.generation}</span>
        <span className="text-xs text-blue-600">{option.bodyType}</span>
      </div>
      <span className="text-xs text-muted-foreground">{option.dateRange}</span>
    </div>
  );

  // Render engine option
  const renderEngineOption = (option: any) => {
    // Extract engine details from the label
    // Format: "1.4 Fire (843A1000) (70 kW / 95 HP) (10.2015–...)"
    const engineNameMatch = option.label.match(/^(.*?)\s*\(/);
    const engineName = engineNameMatch ? engineNameMatch[1].trim() : '';

    const codeMatch = option.label.match(/\(([^)]+)\)/);
    const engineCode = codeMatch ? codeMatch[1] : '';

    const powerMatch = option.label.match(/\((\d+)\s*kW\s*\/\s*(\d+)\s*HP\)/);
    const power = powerMatch ? `${powerMatch[1]} kW / ${powerMatch[2]} HP` : '';

    const dateMatch = option.label.match(/\(([^)]+)\)$/);
    const dateRange = dateMatch ? dateMatch[1] : '';

    return (
      <div className="flex flex-col py-0.5">
        <div className="flex items-center gap-1">
          <span className="font-medium text-sm">{engineName}</span>
          <span className="text-xs bg-gray-100 px-1 rounded">{engineCode}</span>
        </div>
        <div className="flex items-center gap-2 text-xs">
          <span className="text-blue-600">{power}</span>
          <span className="text-gray-500">{dateRange}</span>
        </div>
      </div>
    );
  };

  // Group models by name
  const groupModelsByName = (option: any) => option.model || 'Other';

  // Group engines by fuel type
  const groupEnginesByFuelType = (option: any) => option.fuel_type || 'Other';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">Vehicle Compatibility</DialogTitle>
          <p className="text-muted-foreground mt-1">
            Select compatible vehicles from the database
          </p>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col gap-4">
          {/* Vehicle Type Selector */}
          <div className="border-b pb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium">Vehicle Type</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFilters}
                className="h-8 text-xs"
              >
                Clear Filters
              </Button>
            </div>
            <VehicleTypeSelector
              selectedType={selectedType}
              onTypeChange={handleTypeChange}
            />
          </div>

          {/* Vehicle Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="brand-select">
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-6 h-6 rounded-full bg-red-500 text-white mr-2">
                    1
                  </div>
                  <span>Select car brand</span>
                </div>
              </Label>
              <EnhancedDropdown
                options={brandOptions.map(brand => ({
                  value: brand.name,
                  label: brand.name,
                  image_url: brand.image_url
                }))}
                value={filters.brand}
                onChange={(value) => handleFilterChange('brand', value)}
                placeholder="Search brands..."
                searchPlaceholder="Search for a brand..."
                renderOption={renderBrandOption}
                emptyMessage="No brands found"
                maxHeight="200px"
                className="z-50"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="model-select">
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2">
                    2
                  </div>
                  <span>Select Model</span>
                </div>
              </Label>
              <EnhancedDropdown
                options={modelOptions.map(model => ({
                  value: model.uniqueId,
                  label: model.fullName || `${model.model} (${model.generation})`,
                  model: model.model,
                  generation: model.generation,
                  bodyType: model.bodyType,
                  dateRange: model.dateRange
                }))}
                value={filters.model}
                onChange={(value) => handleFilterChange('model', value)}
                placeholder="Search models..."
                searchPlaceholder="Search for a model..."
                renderOption={renderModelOption}
                groupBy={groupModelsByName}
                emptyMessage="No models found"
                disabled={!filters.brand}
                maxHeight="200px"
                className="z-40"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="engine-select">
                  <div className="flex items-center">
                    <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-700 mr-2">
                      3
                    </div>
                    <span>Select engine (type)</span>
                  </div>
                </Label>
                <Button
                  onClick={handleAddVehicle}
                  disabled={!filters.brand || loading}
                  size="sm"
                  className="h-8"
                >
                  {loading ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : <Plus className="h-4 w-4 mr-1" />}
                  Add
                </Button>
              </div>
              <EnhancedDropdown
                options={engineOptions.map(engine => ({
                  value: engine.fullName,
                  label: engine.fullName,
                  fullName: engine.fullName,
                  code: engine.code,
                  fuel_type: engine.fuel_type,
                  power_kW: engine.power_kW,
                  power_HP: engine.power_HP,
                  productionYears: engine.productionYears
                }))}
                value={filters.engineType}
                onChange={(value) => handleFilterChange('engineType', value)}
                placeholder="Search engine types..."
                searchPlaceholder="Search for an engine..."
                renderOption={renderEngineOption}
                groupBy={groupEnginesByFuelType}
                emptyMessage="No engines found"
                disabled={!filters.model}
                maxHeight="200px"
                className="z-30"
              />
            </div>
          </div>

          {/* Selected Vehicles */}
          <div className="space-y-2 mt-6">
            <div className="flex justify-between items-center">
              <Label className="text-base font-medium">Selected Vehicles ({selectedVehicles.length})</Label>
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search selected vehicles..."
                  className="pl-8 h-9 rounded-full"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="h-[200px] border rounded-xl p-3">
              <ScrollArea className="h-full w-full">
                {filteredVehicles.length > 0 ? (
                  <div className="flex flex-wrap gap-2 p-1">
                    {filteredVehicles.map((vehicle) => (
                      <Badge
                        key={vehicle.id}
                        variant="secondary"
                        className="flex items-center gap-1 py-1.5 px-3 max-w-full rounded-full bg-blue-50 border-blue-200 text-blue-700"
                      >
                        <div className="flex items-center gap-1 overflow-hidden">
                          <Car className="h-3 w-3 mr-1 flex-shrink-0" />
                          <span className="whitespace-normal break-words text-xs font-medium">{vehicle.displayName}</span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-5 w-5 p-0 ml-1 flex-shrink-0 hover:bg-blue-100 rounded-full"
                            onClick={() => handleRemoveVehicle(vehicle.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                    <Car className="h-10 w-10 mb-2 opacity-20" />
                    <p className="text-sm">{searchQuery ? "No matching vehicles found" : "No vehicles selected"}</p>
                    {!searchQuery && <p className="text-xs">Use the filters above to select compatible vehicles</p>}
                  </div>
                )}
              </ScrollArea>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-end gap-2 pt-4 border-t mt-4">
          <Button variant="outline" onClick={handleCancel} className="rounded-full px-6">
            Cancel
          </Button>
          <Button onClick={handleSave} className="rounded-full px-6 bg-blue-600 hover:bg-blue-700">
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default VehicleCompatibilitySelector;
