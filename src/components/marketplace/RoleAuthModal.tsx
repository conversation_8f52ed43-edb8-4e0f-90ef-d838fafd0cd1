import React, { useState, useRef, useEffect } from 'react';
import { X, Phone, Mail, Facebook, Check, Building, Store, FileText, MapPin, Loader2, Eye, EyeOff, Lock, User, Bug } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import i18n from '@/i18n/i18n';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import {
  RoundedDialog as Dialog,
  RoundedDialogContent as DialogContent,
  RoundedDialogHeader as DialogHeader,
  RoundedDialogTitle as DialogTitle,
  RoundedDialogClose as DialogClose,
} from './RoundedDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import {
  signUpWithPhone,
  signInWithPhone,
  signInWithPassword,
  verifyOtp,
  RegistrationData,
  checkForConflictingRole,
  checkPasswordStrength,
  checkEmailExists,
  attemptAuthentication,
  ConflictType,
  RoleConflictResult
} from '@/services/authService';
import { useUser } from '@/contexts/UserContext';
import { useAuth } from '@/contexts/AuthContext';
import { logAuthEvent } from '@/services/auditService';
import { recordAttempt, checkRateLimit, formatTimeRemaining } from '@/services/rateLimitService';
import { getCsrfToken } from '@/utils/csrfProtection';
import { TwoFactorAuth } from '@/components/auth/TwoFactorAuth';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RoleConflictDialog } from './RoleConflictDialog';
import { PasswordStrengthIndicator } from '@/components/auth/PasswordStrengthIndicator';
import { ForgotPasswordForm } from '@/components/auth/ForgotPasswordForm';

export type UserRole = 'supplier' | 'merchant' | 'consumer' | 'distribution';

interface RoleAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  role: UserRole;
  initialMode?: 'signup' | 'login';
}

export function RoleAuthModal({ isOpen, onClose, role, initialMode = 'signup' }: RoleAuthModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { setUserRole, supplierRole, merchantRole, setSupplierRole, setMerchantRole } = useUser();
  const { isRateLimited, incrementLoginAttempt, resetLoginAttempts, setRememberMe, rememberMe } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Get the returnUrl from URL parameters if it exists
  const returnUrl = searchParams.get('returnUrl');


  const [phoneNumber, setPhoneNumber] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [activeTab, setActiveTab] = useState<'signup' | 'login'>(initialMode);
  const [isLoading, setIsLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otpCode, setOtpCode] = useState('');
  const [signupSuccess, setSignupSuccess] = useState(false);
  const phoneInputRef = useRef<HTMLInputElement>(null);
  const otpInputRef = useRef<HTMLInputElement>(null);
  const fullNameInputRef = useRef<HTMLInputElement>(null);
  const emailInputRef = useRef<HTMLInputElement>(null);

  // Role conflict handling
  const [roleConflict, setRoleConflict] = useState(false);
  const [conflictingRole, setConflictingRole] = useState<UserRole | undefined>(undefined);
  const [conflictType, setConflictType] = useState<ConflictType>('none');
  const [conflictResult, setConflictResult] = useState<RoleConflictResult | null>(null);
  const [pendingAuthAction, setPendingAuthAction] = useState<() => Promise<void>>(() => async () => {});

  // Check if user is already authenticated with this role
  const isAlreadyAuthenticated = role === 'supplier' ? supplierRole : role === 'merchant' ? merchantRole : false;

  // Additional fields based on role
  const [companyName, setCompanyName] = useState('');
  const [storeName, setStoreName] = useState('');
  const [storeAddress, setStoreAddress] = useState('');
  const [email, setEmail] = useState('');
  const [fullName, setFullName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [tempUserId, setTempUserId] = useState('');

  // Email validation states
  const [emailExists, setEmailExists] = useState(false);
  const [isCheckingEmail, setIsCheckingEmail] = useState(false);

  // Check email existence when email changes (only for signup) - Fixed version
  useEffect(() => {
    const checkEmail = async () => {
      if (activeTab === 'signup' && email.trim() !== '' && email.includes('@')) {
        setIsCheckingEmail(true);
        try {
          const { exists, role: existingRole } = await checkEmailExists(email, role);
          setEmailExists(exists);
          console.log('Email check result:', { email, exists, existingRole });
        } catch (error) {
          console.error('Error checking email:', error);
          setEmailExists(false);
        } finally {
          setIsCheckingEmail(false);
        }
      } else {
        setEmailExists(false);
        setIsCheckingEmail(false);
      }
    };

    // Debounce the email check to prevent excessive API calls
    const timeoutId = setTimeout(checkEmail, 800);
    return () => clearTimeout(timeoutId);
  }, [email, activeTab, role]);

  // Validate Algerian phone number format
  const validatePhoneNumber = (value: string) => {
    // Algerian phone numbers should be 9 digits after the prefix
    // The prefix is +213 0, and we're only collecting the 9 digits
    const digitsOnly = value.replace(/\D/g, '');

    // For valid Algerian mobile numbers, we need exactly 9 digits
    return digitsOnly.length === 9;
  };

  // Format the phone number as user types
  const formatPhoneNumber = (value: string) => {
    // Remove any non-digit characters
    const digitsOnly = value.replace(/\D/g, '');

    // Format the phone number with spaces for readability
    // Format: XX XXX XX XX (Algerian mobile format)
    if (digitsOnly.length <= 2) {
      return digitsOnly;
    } else if (digitsOnly.length <= 5) {
      return `${digitsOnly.slice(0, 2)} ${digitsOnly.slice(2)}`;
    } else if (digitsOnly.length <= 7) {
      return `${digitsOnly.slice(0, 2)} ${digitsOnly.slice(2, 5)} ${digitsOnly.slice(5)}`;
    } else {
      return `${digitsOnly.slice(0, 2)} ${digitsOnly.slice(2, 5)} ${digitsOnly.slice(5, 7)} ${digitsOnly.slice(7, 9)}`;
    }
  };

  // Handle phone number input change
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Strip any existing formatting and the prefix if it's somehow included
    let digitsOnly = inputValue.replace(/\D/g, '');

    // If the input somehow includes the country code, remove it
    if (digitsOnly.startsWith('213')) {
      digitsOnly = digitsOnly.substring(3);
    }

    // Limit to 9 digits (Algerian mobile format)
    digitsOnly = digitsOnly.slice(0, 9);

    // Format the phone number
    const formattedValue = formatPhoneNumber(digitsOnly);

    setPhoneNumber(formattedValue);
    setIsValid(validatePhoneNumber(digitsOnly));

    // Debug
    console.log('Phone validation:', {
      input: inputValue,
      digitsOnly,
      formattedValue,
      isValid: validatePhoneNumber(digitsOnly)
    });
  };

  // Focus the appropriate input field when the modal opens
  useEffect(() => {
    if (isOpen) {
      console.log(`Modal opened with activeTab: ${activeTab}, initialMode: ${initialMode}`);

      // Use a longer timeout to ensure the modal is fully rendered and animation is complete
      const focusTimeout = setTimeout(() => {
        // For signup tab, focus on the fullName input
        if (activeTab === 'signup' && fullNameInputRef.current) {
          console.log('Focusing fullName input');
          fullNameInputRef.current?.focus();
        }
        // For login tab, focus on the email input
        else if (activeTab === 'login' && emailInputRef.current) {
          console.log('Focusing email input');
          emailInputRef.current?.focus();
        }
        // For OTP verification, focus on the OTP input
        else if (otpSent && otpInputRef.current) {
          console.log('Focusing OTP input');
          otpInputRef.current?.focus();
        }
      }, 300);

      return () => clearTimeout(focusTimeout);
    }
  }, [isOpen, activeTab, otpSent, initialMode]);

  // Update activeTab when initialMode or isOpen changes
  useEffect(() => {
    if (isOpen) {
      console.log(`RoleAuthModal: Modal opened or initialMode changed to ${initialMode}, updating activeTab`);
      setActiveTab(initialMode);
    }
  }, [initialMode, isOpen]);

  // Reset loading state when OTP screen is shown
  useEffect(() => {
    if (otpSent) {
      setIsLoading(false);
    }
  }, [otpSent]);

  // Reset only critical form state when modal opens (minimal reset to prevent input jumping)
  useEffect(() => {
    if (isOpen) {
      console.log('Modal opened, performing minimal state reset');
      setIsLoading(false);
      setSignupSuccess(false);
      setOtpSent(false);
      setShowTwoFactor(false);
      setShowForgotPassword(false);
      // Don't reset email checking states or form values to prevent input jumping
    }
  }, [isOpen]);

  // Validate form based on role and tab
  const validateForm = () => {
    // For OTP verification
    if (otpSent) {
      return otpCode.trim().length >= 6;
    }

    // For forgot password form
    if (showForgotPassword) {
      return email.trim() !== '';
    }



    // For signup
    if (activeTab === 'signup') {
      const emailValid = email.trim() !== '';
      const passwordValid = password.trim().length >= 8;
      const passwordsMatch = password === confirmPassword;
      const passwordStrengthValid = checkPasswordStrength(password).isValid;
      // Phone is now REQUIRED and must be valid
      const phoneValid = phoneNumber.trim() !== '' && isValid;

      // Log validation state for debugging
      console.log('Form validation:', {
        emailValid,
        emailExists,
        isCheckingEmail,
        passwordValid,
        passwordsMatch,
        passwordStrengthValid,
        phoneValid,
        fullName: fullName.trim() !== '',
        companyName: role === 'supplier' ? companyName.trim() !== '' : 'N/A',
        storeName: role === 'merchant' ? storeName.trim() !== '' : 'N/A',
        storeAddress: role === 'merchant' ? storeAddress.trim() !== '' : 'N/A'
      });

      // Common validation for all roles (with proper email checking)
      const commonValid = emailValid && passwordValid && passwordsMatch && passwordStrengthValid && phoneValid && fullName.trim() !== '' && !emailExists && !isCheckingEmail;

      if (role === 'supplier') {
        return commonValid && companyName.trim() !== '';
      } else if (role === 'merchant') {
        return commonValid && storeName.trim() !== '' && storeAddress.trim() !== '';
      }

      return commonValid;
    }

    // For login (password only)
    if (activeTab === 'login') {
      return email.trim() !== '' && password.trim() !== '';
    }

    return false;
  };

  // Function to check for role conflicts before proceeding with authentication
  const checkRoleConflictAndProceed = async (authAction: () => Promise<void>) => {
    // Check if there's a conflicting role, including email check for cross-account conflicts
    const { hasConflict, conflictingRole: detectedConflictingRole } = await checkForConflictingRole(
      role,
      activeTab === 'login' ? email : undefined // Only check email for login, not signup
    );

    if (hasConflict && detectedConflictingRole) {
      // Store the auth action to execute after conflict resolution
      setPendingAuthAction(() => authAction);
      setConflictingRole(detectedConflictingRole);
      setRoleConflict(true);
      setIsLoading(false);
      return false;
    }

    // No conflict, proceed with authentication
    try {
      await authAction();
      return true;
    } catch (error) {
      console.error('Error in auth action:', error);
      setIsLoading(false);
      return false;
    }
  };

  // Function to handle OTP verification
  const handleOtpVerification = async () => {
    const formattedPhone = '+213 0' + phoneNumber.replace(/\s/g, '');

    try {
      // The authService will handle development mode internally

      // Normal OTP verification flow
      const { data, error } = await verifyOtp(formattedPhone, otpCode);

      if (error) {
        toast({
          title: t('auth.verificationFailed'),
          description: error.message,
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      if (data.session) {
        toast({
          title: t('auth.verificationSuccess'),
          description: activeTab === 'signup'
            ? t('auth.registrationSuccess')
            : t('auth.loginSuccess'),
        });

        // Set the active role flag
        if (role === 'supplier') {
          setSupplierRole(true);
        } else if (role === 'merchant') {
          setMerchantRole(true);
        }

        // Set the current user role
        setUserRole(role);

        // Close the modal and redirect immediately
        handleClose();

        // Dispatch authentication success event
        const authSuccessEvent = new CustomEvent('auth-success', {
          detail: { role, mode: activeTab }
        });
        document.dispatchEvent(authSuccessEvent);

        // Use direct browser redirect for more reliable navigation
        const redirectUrl = returnUrl || '/app/dashboard';
        console.log('Performing hard redirect to:', redirectUrl);
        window.location.href = redirectUrl;
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      toast({
        title: t('auth.verificationFailed'),
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }

    setIsLoading(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm() || isLoading) return;

    setIsLoading(true);

    try {
      // If showing forgot password form, don't proceed with normal auth flow
      if (showForgotPassword) {
        return;
      }

      // If OTP has been sent, verify it
      if (otpSent) {
        await handleOtpVerification();
        return;
      }

      // For signup or login, check for role conflicts first
      if (activeTab === 'signup') {
        const signupAction = async () => {
          try {
            console.log('Starting signup process...');

            // Validate passwords match
            if (password !== confirmPassword) {
              toast({
                title: t('auth.passwordMismatch'),
                description: t('auth.passwordMismatchDescription'),
                variant: "destructive",
              });
              setIsLoading(false);
              return;
            }

            // Validate password strength
            const passwordCheck = checkPasswordStrength(password);
            if (!passwordCheck.isValid) {
              toast({
                title: t('auth.passwordTooWeak'),
                description: t('auth.passwordRequirements'),
                variant: "destructive",
              });
              setIsLoading(false);
              return;
            }

            // Phone is required and must be valid
            if (phoneNumber.trim() === '' || !isValid) {
              toast({
                title: "Phone Number Required",
                description: "Please enter a valid Algerian phone number.",
                variant: "destructive",
              });
              setIsLoading(false);
              return;
            }

            // Format the phone number if provided
            let formattedPhone = undefined;
            if (phoneNumber.trim() !== '') {
              // Only include the phone number if it's not empty
              const digitsOnly = phoneNumber.replace(/\D/g, '');
              if (digitsOnly && !/^0+$/.test(digitsOnly)) {
                formattedPhone = '+213 0' + phoneNumber.replace(/\s/g, '');
                console.log('Preparing registration data:', { email, role, fullName, phoneNumber: formattedPhone });
              } else {
                console.log('Preparing registration data:', { email, role, fullName, phoneNumber: 'Invalid phone (only zeros)' });
              }
            } else {
              console.log('Preparing registration data:', { email, role, fullName, phoneNumber: 'Not provided' });
            }

            // Split full name into first and last name for the database
            let firstName = '';
            let lastName = '';

            if (fullName) {
              const nameParts = fullName.split(' ');
              firstName = nameParts[0] || '';
              lastName = nameParts.slice(1).join(' ') || '';
            }

            const registrationData: RegistrationData = {
              email,
              password,
              role,
              fullName, // Keep the full name for display purposes
              firstName, // Add first name for database
              lastName,  // Add last name for database
              ...(formattedPhone ? { phone: formattedPhone } : {}), // Only include phone if provided
              ...(role === 'supplier' ? { companyName } : {}),
              ...(role === 'merchant' ? { storeName, storeAddress } : {})
            };

            console.log('Calling signUpWithPhone with data:', { ...registrationData, password: '***' });

            // Always use the authService which will handle development mode internally
            const result = await signUpWithPhone(registrationData);
            const user = result.user;
            const error = result.error;

            console.log('signUpWithPhone response:', { user: user ? 'User object received' : 'No user', error });

            if (error) {
              console.error('Registration error:', error);

              // Handle specific error cases more gracefully
              if (error.message.includes('already registered')) {
                toast({
                  title: t('auth.emailAlreadyExists'),
                  description: 'This email is already registered. Please use the login option instead.',
                  variant: "destructive",
                });
              } else {
                toast({
                  title: t('auth.registrationFailed'),
                  description: error.message,
                  variant: "destructive",
                });
              }
              setIsLoading(false);
              return;
            }

            // If we get here, the signup was successful
            console.log('Signup successful, proceeding to dashboard immediately');

            // For production: No email verification required, redirect immediately
            console.log('Signup completed successfully, redirecting to dashboard');

            // Update user role context
            if (role === 'supplier') {
              setSupplierRole(user);
            } else if (role === 'merchant') {
              setMerchantRole(user);
            }
            setUserRole(role);

            // Show success toast
            toast({
              title: t('auth.registrationSuccess'),
              description: `Welcome to AROUZ MARKET! Redirecting to your dashboard...`,
              duration: 3000,
            });

            // Close modal and redirect immediately
            onClose();

            // Use direct browser redirect for more reliable navigation
            const redirectUrl = returnUrl || '/app/dashboard';
            console.log('Performing immediate redirect to:', redirectUrl);
            window.location.href = redirectUrl;
          } catch (err) {
            console.error('Unexpected error in signup action:', err);
            toast({
              title: "Registration Error",
              description: "An unexpected error occurred. Please try again.",
              variant: "destructive",
            });
            setIsLoading(false);
            return;
          }
        };

        // Check for conflicts before proceeding
        // The signupAction will be called by checkRoleConflictAndProceed if there's no conflict
        // or if the user confirms they want to proceed despite the conflict
        await checkRoleConflictAndProceed(signupAction);
      } else {
        // Handle login with password (only option now)
        const loginWithPasswordAction = async () => {
          // Check for rate limiting
          if (isRateLimited) {
            const { timeRemaining } = checkRateLimit('LOGIN_USER', email);
            toast({
              title: t('auth.tooManyAttempts'),
              description: t('auth.tryAgainLater', { time: formatTimeRemaining(timeRemaining) }),
              variant: "destructive",
            });
            setIsLoading(false);
            return;
          }

          // Add CSRF token to request
          const csrfToken = getCsrfToken();

          // Use enhanced authentication with comprehensive role conflict detection
          const authResult = await attemptAuthentication(email, password, role);

          if (!authResult.success) {
            // Check if it's a role conflict
            if (authResult.conflictResult?.hasConflict) {
              console.log('Role conflict detected during authentication:', authResult.conflictResult);

              // Store conflict information for the dialog
              setConflictResult(authResult.conflictResult);
              setConflictType(authResult.conflictResult.conflictType);
              setConflictingRole(authResult.conflictResult.conflictingRole);
              setRoleConflict(true);
              setIsLoading(false);
              return;
            }

            // Handle other authentication errors
            const error = authResult.error;
            if (error) {
              // Record failed attempt for rate limiting
              incrementLoginAttempt();
              recordAttempt('LOGIN_USER', email);

              // Log failed login attempt
              await logAuthEvent('unknown', 'login_failed', {
                method: 'password',
                email,
                reason: error.message,
                role
              });

              toast({
                title: t('auth.loginFailed'),
                description: error.message,
                variant: "destructive",
              });
              setIsLoading(false);
              return;
            }
          }

          const user = authResult.user;

          if (user) {
            // Reset login attempts on successful login
            resetLoginAttempts();

            // Log successful login
            await logAuthEvent(user.id, 'login', {
              method: 'password',
              role
            });

            // Check if two-factor authentication is required
            const requiresTwoFactor = false; // This would be determined by user settings

            if (requiresTwoFactor) {
              // Show two-factor authentication screen
              setTempUserId(user.id);
              setShowTwoFactor(true);
              setIsLoading(false);
              return;
            }

            toast({
              title: t('auth.loginSuccess'),
              description: t('auth.welcomeBack'),
            });

            // Set the active role flag
            if (role === 'supplier') {
              setSupplierRole(true);
            } else if (role === 'merchant') {
              setMerchantRole(true);
            }

            // Set the current user role
            setUserRole(role);

            // Close the modal
            handleClose();

            // Dispatch authentication success event
            const authSuccessEvent = new CustomEvent('auth-success', {
              detail: { role, mode: activeTab }
            });
            document.dispatchEvent(authSuccessEvent);

            // Use direct browser redirect for more reliable navigation
            const redirectUrl = returnUrl || '/app/dashboard';
            console.log('Performing hard redirect to:', redirectUrl);
            window.location.href = redirectUrl;
          }
        };

        // Check for conflicts before proceeding
        if (await checkRoleConflictAndProceed(loginWithPasswordAction)) {
          await loginWithPasswordAction();
        }
      }
    } catch (error) {
      console.error('Authentication error:', error);

      // Provide more detailed error information
      let errorMessage = t('auth.unexpectedError');
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: t('auth.authenticationError'),
        description: errorMessage,
        variant: "destructive",
      });

      setIsLoading(false);
    }
  };

  // Reset form state
  const resetForm = () => {
    setPhoneNumber('');
    setEmail('');
    setCompanyName('');
    setStoreName('');
    setStoreAddress('');
    setFullName('');
    setPassword('');
    setConfirmPassword('');
    setOtpCode('');
    setOtpSent(false);
    setIsValid(false);
    // Keep the initialMode when resetting the form
    setActiveTab(initialMode);
    setIsLoading(false);
    setShowPassword(false);
    setShowConfirmPassword(false);
    // Reset email validation states
    setEmailExists(false);
    setIsCheckingEmail(false);

    setShowForgotPassword(false);
    setShowTwoFactor(false);
    setTempUserId('');
    setSignupSuccess(false);
  };

  // Handle modal close
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Get title based on role and tab
  const getTitle = () => {
    if (signupSuccess) {
      return t('auth.registrationSuccess');
    } else if (showTwoFactor) {
      return t('auth.twoFactorAuthentication');
    } else if (otpSent) {
      return t('auth.verifyOtp');
    } else if (activeTab === 'signup') {
      return role === 'supplier' ? t('auth.supplierSignUp') : t('auth.merchantSignUp');
    } else {
      return role === 'supplier' ? t('auth.supplierLogin') : t('auth.merchantLogin');
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={handleClose}
      onOpenAutoFocus={(e) => {
        // Prevent default focus behavior to avoid focus stealing
        e.preventDefault();
        // We'll handle focus manually
      }}
    >
      <DialogContent
        className="sm:max-w-md p-0 gap-0 overflow-hidden max-h-[90vh] w-[95vw] sm:w-auto"
        onAnimationEnd={() => {
          // Focus the appropriate input after animation completes
          if (!isAlreadyAuthenticated) {
            setTimeout(() => {
              // For signup tab, focus on the fullName input
              if (activeTab === 'signup' && fullNameInputRef.current) {
                fullNameInputRef.current?.focus();
              }
              // For login tab, focus on the email input
              else if (activeTab === 'login' && emailInputRef.current) {
                emailInputRef.current?.focus();
              }
            }, 50);
          }
        }}
      >
        {/* Role conflict dialog */}
        <RoleConflictDialog
          isOpen={roleConflict}
          onClose={() => {
            setRoleConflict(false);
            setConflictResult(null);
            setConflictType('none');
            setConflictingRole(undefined);
          }}
          role={role}
          conflictingRole={conflictingRole || 'supplier'}
          conflictType={conflictType}
          userEmail={email}
          onContinue={() => {
            // Execute the pending auth action after conflict resolution
            pendingAuthAction();
          }}
        />
        {/* Show alert if user is already authenticated with this role */}
        {isAlreadyAuthenticated && (
          <Alert className="m-4 bg-amber-50 text-amber-800 border-amber-200">
            <AlertDescription>
              {role === 'supplier'
                ? t('auth.alreadyAuthenticatedAsSupplier')
                : t('auth.alreadyAuthenticatedAsMerchant')}
            </AlertDescription>
          </Alert>
        )}
        <DialogHeader className="p-4 pb-2 text-center border-b">
          <DialogClose onClick={handleClose}>
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogClose>



          <DialogTitle className="text-lg font-semibold">
            {getTitle()}
          </DialogTitle>
        </DialogHeader>

        {signupSuccess ? (
          <div className="p-6 pt-4 max-h-[70vh] overflow-y-auto">
            <div className="flex flex-col items-center justify-center text-center space-y-6">
              {/* Success icon with enhanced animation */}
              <div className="relative">
                <div className="h-24 w-24 rounded-full bg-gradient-to-br from-[#FEE2E2] to-[#DC2626]/20 flex items-center justify-center shadow-lg animate-pulse">
                  <div className="h-20 w-20 rounded-full bg-[#FEE2E2] flex items-center justify-center">
                    <svg className="h-12 w-12 text-[#DC2626] animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                {/* Decorative rings */}
                <div className="absolute inset-0 rounded-full border-2 border-[#DC2626]/20 animate-ping"></div>
                <div className="absolute inset-2 rounded-full border border-[#DC2626]/10 animate-ping" style={{animationDelay: '0.5s'}}></div>
              </div>

              {/* Success heading with better typography */}
              <div className="space-y-2">
                <h2 className="text-3xl font-bold text-[#071c44] tracking-tight">{t('auth.registrationSuccess')}</h2>
                <p className="text-lg text-gray-600 font-medium">{t('auth.welcomeToArouz')}</p>
              </div>

              {/* Email verification info box - Enhanced design */}
              <div className={cn(
                "w-full bg-gradient-to-r from-[#FEE2E2]/20 to-[#DC2626]/10 border border-[#DC2626]/30 rounded-xl p-5 shadow-sm backdrop-blur-sm",
                i18n.language === 'ar' ? "text-right" : "text-left"
              )}>
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    <div className="h-8 w-8 rounded-full bg-[#DC2626] flex items-center justify-center">
                      <svg className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-[#071c44] mb-2 text-base">{t('auth.emailVerificationRequired')}</h3>
                    <p className="text-gray-700 text-sm mb-2 leading-relaxed">
                      {t('auth.emailSentTo')} <span className="font-semibold text-[#DC2626] break-all">{email}</span>
                    </p>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {t('auth.verificationLinkInstructions')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Next steps section - Enhanced design */}
              <div className="w-full bg-white rounded-xl border border-gray-100 p-5 shadow-sm">
                <div className="flex items-center gap-2 mb-4">
                  <div className="h-6 w-6 rounded-full bg-[#071c44] flex items-center justify-center">
                    <span className="text-white text-xs font-bold">!</span>
                  </div>
                  <p className={cn(
                    "text-[#071c44] font-semibold text-base",
                    i18n.language === 'ar' ? "text-right" : "text-left"
                  )}>
                    {t('auth.whatToDoNext')}
                  </p>
                </div>
                <div className="space-y-3">
                  {[
                    { key: 'checkInbox', icon: '📧' },
                    { key: 'openEmail', icon: '📬' },
                    { key: 'clickVerificationLink', icon: '🔗' },
                    { key: 'loginAfterVerification', icon: '✅' }
                  ].map((step, index) => (
                    <div
                      key={step.key}
                      className={cn(
                        "flex items-start gap-3 p-3 rounded-lg bg-gray-50 transition-all duration-300 hover:bg-gray-100",
                        i18n.language === 'ar' ? "text-right" : "text-left"
                      )}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <span className="text-lg flex-shrink-0 mt-0.5">{step.icon}</span>
                      <span className="text-gray-700 text-sm leading-relaxed flex-1">{t(`auth.${step.key}`)}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action button */}
              <div className="w-full pt-2">
                <Button
                  onClick={handleClose}
                  className="w-full bg-gradient-to-r from-[#DC2626] to-[#DC2626]/90 hover:from-[#DC2626]/90 hover:to-[#DC2626]/80 text-white font-semibold py-4 px-8 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]"
                >
                  <span className="flex items-center justify-center gap-2">
                    {t('auth.gotIt')}
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </span>
                </Button>
              </div>
            </div>
          </div>
        ) : showTwoFactor ? (
          <div className="p-4 pt-2 max-h-[60vh] overflow-y-auto">
            <TwoFactorAuth
              userId={tempUserId}
              onVerified={() => {
                // Set the active role flag
                if (role === 'supplier') {
                  setSupplierRole(true);
                } else if (role === 'merchant') {
                  setMerchantRole(true);
                }

                // Set the current user role
                setUserRole(role);

                // Close the modal
                handleClose();

                // Dispatch authentication success event
                const authSuccessEvent = new CustomEvent('auth-success', {
                  detail: { role, mode: activeTab }
                });
                document.dispatchEvent(authSuccessEvent);

                // Use direct browser redirect for more reliable navigation
                const redirectUrl = returnUrl || '/app/dashboard';
                console.log('Performing hard redirect to:', redirectUrl);
                window.location.href = redirectUrl;
              }}
              onCancel={() => {
                setShowTwoFactor(false);
                setIsLoading(false);
              }}
            />
          </div>
        ) : otpSent ? (
          <div className="p-4 pt-2 max-h-[60vh] overflow-y-auto">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-1.5">
                <Label htmlFor="otp" className="text-sm font-medium">
                  {t('auth.otpCode')}
                </Label>
                <div className="relative">
                  <Input
                    id="otp"
                    ref={otpInputRef}
                    value={otpCode}
                    onChange={(e) => setOtpCode(e.target.value)}
                    placeholder="Enter OTP code"
                    className="w-full h-10"
                    inputMode="numeric"
                    required
                    autoComplete="one-time-code"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {t('auth.otpDescription')}
                </p>
              </div>

              <Button
                type="submit"
                disabled={!validateForm() || isLoading}
                className={cn(
                  "w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white font-semibold py-2.5 rounded-lg",
                  (!validateForm() || isLoading) && "opacity-50 cursor-not-allowed"
                )}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('auth.verifying')}
                  </>
                ) : (
                  t('auth.verify')
                )}
              </Button>

              <p className="text-sm text-center text-gray-500">
                {t('auth.didntReceiveCode')} <button type="button" onClick={() => {
                  setOtpSent(false);
                  setIsLoading(false);
                }} className="text-[#DC2626] hover:underline">{t('auth.tryAgain')}</button>
              </p>
            </form>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'signup' | 'login')} className="w-full">
            <div className="px-4 pt-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="signup">{t('auth.signUp')}</TabsTrigger>
                <TabsTrigger value="login">{t('auth.login')}</TabsTrigger>
              </TabsList>
            </div>

          <TabsContent value="signup" className="p-4 pt-2 max-h-[60vh] overflow-y-auto">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Common fields for all roles */}
              <div className="space-y-1.5">
                <Label htmlFor="fullName" className="text-sm font-medium">
                  {t('auth.fullName')}
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <User className="h-4 w-4" />
                  </div>
                  <Input
                    id="fullName"
                    ref={fullNameInputRef}
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder={t('auth.enterFullName')}
                    className="w-full pl-10 h-10"
                    required
                  />
                </div>
              </div>

              {/* Role-specific fields for signup */}
              {role === 'supplier' && (
                <div className="space-y-1.5">
                  <Label htmlFor="companyName" className="text-sm font-medium">
                    {t('auth.companyName')}
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                      <Building className="h-4 w-4" />
                    </div>
                    <Input
                      id="companyName"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      placeholder={t('auth.enterCompanyName')}
                      className="w-full pl-10 h-10"
                      required
                    />
                  </div>
                </div>
              )}

              {role === 'merchant' && (
                <>
                  <div className="space-y-1.5">
                    <Label htmlFor="storeName" className="text-sm font-medium">
                      {t('auth.storeName')}
                    </Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                        <Store className="h-4 w-4" />
                      </div>
                      <Input
                        id="storeName"
                        value={storeName}
                        onChange={(e) => setStoreName(e.target.value)}
                        placeholder={t('auth.enterStoreName')}
                        className="w-full pl-10 h-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="storeAddress" className="text-sm font-medium">
                      {t('auth.storeAddress')}
                    </Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                        <MapPin className="h-4 w-4" />
                      </div>
                      <Input
                        id="storeAddress"
                        value={storeAddress}
                        onChange={(e) => setStoreAddress(e.target.value)}
                        placeholder={t('auth.enterStoreAddress')}
                        className="w-full pl-10 h-10"
                        required
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Email field */}
              <div className="space-y-1.5">
                <Label htmlFor="email" className="text-sm font-medium">
                  Email
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <Mail className="h-4 w-4" />
                  </div>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className={cn(
                      "w-full pl-10 h-10",
                      emailExists && "border-red-500 focus:border-red-500 focus:ring-red-500"
                    )}
                    required
                  />
                  {isCheckingEmail && (
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                    </div>
                  )}
                </div>
                {emailExists && (
                  <p className="text-xs text-red-500 mt-1">
                    This email is already registered. Please use a different email or try logging in.
                  </p>
                )}
              </div>

              {/* Password field */}
              <div className="space-y-1.5">
                <Label htmlFor="password" className="text-sm font-medium">
                  {t('auth.password')}
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <Lock className="h-4 w-4" />
                  </div>
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder={t('auth.enterPassword')}
                    className="w-full pl-10 pr-10 h-10"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>

                {password && <PasswordStrengthIndicator password={password} className="mt-2" />}
              </div>

              {/* Confirm Password field */}
              <div className="space-y-1.5">
                <Label htmlFor="confirmPassword" className="text-sm font-medium">
                  {t('auth.confirmPassword')}
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <Lock className="h-4 w-4" />
                  </div>
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder={t('auth.confirmPassword')}
                    className="w-full pl-10 pr-10 h-10"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {password && confirmPassword && password !== confirmPassword && (
                  <p className="text-xs text-red-500 mt-1">
                    {t('auth.passwordMismatchDescription')}
                  </p>
                )}
              </div>

              {/* Phone number field - REQUIRED for signup */}
              <div className="space-y-1.5">
                <Label htmlFor="phone" className="text-sm font-medium flex items-center">
                  {t('auth.phoneNumber')} <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500 font-medium">
                    +213 0
                  </div>
                  <Input
                    id="phone"
                    type="tel"
                    ref={phoneInputRef}
                    placeholder="Enter XX XXX XX XX"
                    value={phoneNumber}
                    onChange={handlePhoneChange}
                    className="w-full pl-16 h-10"
                    inputMode="numeric"
                    autoComplete="tel"
                    required
                    onFocus={(e) => {
                      // If the field is empty, position cursor at the beginning
                      if (!phoneNumber) {
                        e.target.setSelectionRange(0, 0);
                      }
                    }}
                  />
                  {isValid && phoneNumber.length > 0 && (
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 text-green-500">
                      <Check className="h-4 w-4" />
                    </div>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {t('auth.phoneFormat')}
                </p>
                {!isValid && phoneNumber.length > 0 && (
                  <p className="text-xs text-red-500 mt-1">
                    Please enter a valid 9-digit Algerian phone number
                  </p>
                )}
              </div>

              <p className="text-xs text-gray-500">
                {t('auth.privacyNotice')}
                <a href="/privacy-policy" className="text-[#DC2626] hover:underline ml-1">
                  {t('auth.privacyPolicy')}
                </a>
              </p>

              <Button
                type="submit"
                disabled={!validateForm() || isLoading}
                className={cn(
                  "w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white font-semibold py-2.5 rounded-lg",
                  (!validateForm() || isLoading) && "opacity-50 cursor-not-allowed"
                )}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('auth.processing')}
                  </>
                ) : (
                  t('auth.signUp')
                )}
              </Button>

              <p className="text-sm text-center text-gray-500">
                {t('auth.alreadyHaveAccount')} <button type="button" onClick={() => setActiveTab('login')} className="text-[#DC2626] hover:underline">{t('auth.login')}</button>
              </p>
            </form>
          </TabsContent>

          <TabsContent value="login" className="p-4 pt-2 max-h-[60vh] overflow-y-auto">
            {showForgotPassword ? (
              <ForgotPasswordForm onBack={() => setShowForgotPassword(false)} />
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Password is the only authentication method now */}

                {/* Email field (common for both methods) */}
                <div className="space-y-1.5">
                  <Label htmlFor="email-login" className="text-sm font-medium">
                    Email
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                      <Mail className="h-4 w-4" />
                    </div>
                    <Input
                      id="email-login"
                      type="email"
                      ref={emailInputRef}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      className="w-full pl-10 h-10"
                      required
                    />
                  </div>
                </div>

                {/* Password field */}
                <div className="space-y-1.5">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="password-login" className="text-sm font-medium">
                      {t('auth.password')}
                    </Label>
                    <button
                      type="button"
                      className="text-xs text-[#DC2626] hover:underline"
                      onClick={() => setShowForgotPassword(true)}
                    >
                      {t('auth.forgotPassword')}
                    </button>
                  </div>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                      <Lock className="h-4 w-4" />
                    </div>
                    <Input
                      id="password-login"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder={t('auth.enterPassword')}
                      className="w-full pl-10 pr-10 h-10"
                      required
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                {/* Remember Me checkbox */}
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="remember-me"
                    className="h-4 w-4 rounded border-gray-300 text-[#DC2626] focus:ring-[#DC2626]"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  <label htmlFor="remember-me" className="text-sm text-gray-600">
                    {t('auth.rememberMe')}
                  </label>
                </div>

                {/* Rate limit warning */}
                {isRateLimited && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                    {t('auth.tooManyAttempts')}. {t('auth.tryAgainLater')}
                  </div>
                )}

                <Button
                  type="submit"
                  disabled={!validateForm() || isLoading || isRateLimited}
                  className={cn(
                    "w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white font-semibold py-2.5 rounded-lg",
                    (!validateForm() || isLoading || isRateLimited) && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('auth.processing')}
                    </>
                  ) : (
                    t('auth.login')
                  )}
                </Button>

                <p className="text-sm text-center text-gray-500">
                  {t('auth.dontHaveAccount')} <button type="button" onClick={() => setActiveTab('signup')} className="text-[#DC2626] hover:underline">{t('auth.signUp')}</button>
                </p>
              </form>
            )}
          </TabsContent>
        </Tabs>
        )}
      </DialogContent>

      {/* Role Conflict Dialog */}
      {roleConflict && conflictingRole && (
        <RoleConflictDialog
          isOpen={roleConflict}
          onClose={() => setRoleConflict(false)}
          role={role}
          conflictingRole={conflictingRole}
          onContinue={async () => {
            // Execute the pending authentication action after conflict resolution
            setIsLoading(true);
            try {
              await pendingAuthAction();
            } catch (error) {
              console.error('Error during authentication after conflict resolution:', error);
              toast({
                title: "Authentication Error",
                description: "An unexpected error occurred. Please try again.",
                variant: "destructive",
              });
              setIsLoading(false);
            }
          }}
        />
      )}
    </Dialog>
  );
}
