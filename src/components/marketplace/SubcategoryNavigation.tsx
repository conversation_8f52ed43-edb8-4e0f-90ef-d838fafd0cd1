import React, { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { FileText, ChevronLeft, ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { useFilter } from '@/contexts/FilterContext';
import { getSubcategoriesByCategory, getSubcategoryImageUrl } from '@/data/categoryData';
import { getFallbackImageUrl } from '@/services/categoryImageService';
import { useLegacyCategories, useMigrationStatus } from '@/data/categoryDataSupabase';

// Subcategory data is now centralized in @/data/categoryData

interface SubcategoryNavigationProps {
  selectedCategory: string;
  onSubcategorySelect: (subcategory: string) => void;
  selectedSubcategory: string;
  onRequestQuote: () => void;
}

export function SubcategoryNavigation({
  selectedCategory,
  onSubcategorySelect,
  selectedSubcategory,
  onRequestQuote
}: SubcategoryNavigationProps) {
  const { t } = useTranslation();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);

  // Use Supabase categories with localStorage fallback
  const { categories: supabaseCategories, isLoading: categoriesLoading } = useLegacyCategories();
  const { isMigrated, shouldUseFallback } = useMigrationStatus();

  // Get subcategories based on selected category using new system
  const getSubcategories = () => {
    if (isMigrated) {
      // Use Supabase data
      const category = supabaseCategories.find(cat => cat.id === selectedCategory);
      return category?.subcategories || [];
    } else {
      // Fallback to localStorage data
      return getSubcategoriesByCategory(selectedCategory);
    }
  };

  const subcategories = getSubcategories();

  // Check scroll position to show/hide arrows
  const checkScroll = () => {
    if (!scrollContainerRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10); // 10px buffer
  };

  // Scroll left/right
  const scroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;

    const scrollAmount = 300; // Adjust as needed
    const newScrollLeft = direction === 'left'
      ? scrollContainerRef.current.scrollLeft - scrollAmount
      : scrollContainerRef.current.scrollLeft + scrollAmount;

    scrollContainerRef.current.scrollTo({
      left: newScrollLeft,
      behavior: 'smooth'
    });
  };

  // Listen for scroll events
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScroll);
      // Initial check
      checkScroll();

      return () => scrollContainer.removeEventListener('scroll', checkScroll);
    }
  }, []);

  // Check scroll when window resizes
  useEffect(() => {
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, []);

  // Don't render if no category is selected or no subcategories available
  if (!selectedCategory || subcategories.length === 0) {
    return null;
  }

  return (
    <div className="mb-4">
      {/* Airbnb-style Horizontal Scrollable Subcategories */}
      <div className="relative">
        {/* Left scroll arrow */}
        {showLeftArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 h-8 w-8 bg-white/90 shadow-md hover:bg-white rounded-full border border-gray-200 hidden sm:flex"
            onClick={() => scroll('left')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}

        {/* Scrollable subcategories container */}
        <div
          ref={scrollContainerRef}
          className="flex items-center gap-3 md:gap-4 overflow-x-auto scrollbar-hide px-2 sm:px-10"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {subcategories.map((subcategory) => {
            // Smart text wrapping logic for subcategories - based on character count (18+ chars including spaces)
            const shouldWrap = subcategory.displayName.length >= 18;

            return (
              <div key={subcategory.id} className="flex flex-col items-center min-w-[85px] md:min-w-[105px]">
                <button
                  className={cn(
                    "flex flex-col items-center gap-1 md:gap-2 py-1 md:py-2 px-2 md:px-3 transition-all duration-75 min-w-[75px] md:min-w-[95px] group",
                    "hover:opacity-80 active:scale-95",
                    selectedSubcategory === subcategory.name
                      ? "opacity-100"
                      : "opacity-70 hover:opacity-90"
                  )}
                  onClick={() => onSubcategorySelect(subcategory.name)}
                >
                  {/* Image container - Circular shape, increased size */}
                  <div className={cn(
                    "w-14 h-14 md:w-18 md:h-18 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden transition-all duration-75",
                    selectedSubcategory === subcategory.name
                      ? "ring-2 ring-[#DC2626] ring-offset-1 md:ring-offset-2"
                      : "group-hover:bg-gray-300"
                  )}>
                    <img
                      src={getSubcategoryImageUrl(subcategory.id)}
                      alt={subcategory.displayName}
                      className="w-10 h-10 md:w-12 md:h-12 object-contain"
                      onError={(e) => {
                        // Fallback to placeholder on error
                        const target = e.target as HTMLImageElement;
                        target.src = getFallbackImageUrl('subcategory');
                      }}
                    />
                  </div>

                  {/* Subcategory name - Smart text wrapping */}
                  <div className={cn(
                    "text-center transition-colors duration-75 flex items-center justify-center",
                    shouldWrap ? "h-6 md:h-8 leading-tight" : "h-3 md:h-4",
                    selectedSubcategory === subcategory.name
                      ? "text-gray-900"
                      : "text-gray-600 group-hover:text-gray-900"
                  )}>
                    <span className={cn(
                      "font-medium",
                      shouldWrap
                        ? "text-[9px] md:text-[10px] leading-3 max-w-[60px] md:max-w-[80px]"
                        : "text-[10px] md:text-xs whitespace-nowrap"
                    )}>
                      {subcategory.displayName}
                    </span>
                  </div>
                </button>

                {/* Request Quote Button - Only show for selected subcategory */}
                {selectedSubcategory === subcategory.name && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-1 md:mt-2 text-[10px] md:text-xs h-6 md:h-7 border-[#DC2626] text-[#DC2626] hover:bg-[#DC2626] hover:text-white px-2 md:px-3"
                    onClick={onRequestQuote}
                  >
                    <FileText className="h-2 w-2 md:h-3 md:w-3 mr-1" />
                    <span className="hidden sm:inline">Request</span>
                    <span className="sm:hidden">Quote</span>
                  </Button>
                )}
              </div>
            );
          })}
        </div>

        {/* Right scroll arrow */}
        {showRightArrow && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 h-8 w-8 bg-white/90 shadow-md hover:bg-white rounded-full border border-gray-200 hidden sm:flex"
            onClick={() => scroll('right')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
