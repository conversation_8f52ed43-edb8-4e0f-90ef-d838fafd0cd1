import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Check, ChevronsUpDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { useClickAway } from 'react-use';

interface Option {
  value: string;
  label: string;
  image_url?: string;
  [key: string]: any;
}

interface AirbnbSearchDropdownProps {
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  renderOption?: (option: Option) => React.ReactNode;
  isActive: boolean;
  onFocus: () => void;
  disabled?: boolean;
}

export function AirbnbSearchDropdown({
  options,
  value,
  onChange,
  placeholder,
  searchPlaceholder = 'Search...',
  emptyMessage = 'No results found',
  renderOption,
  isActive,
  onFocus,
  disabled = false
}: AirbnbSearchDropdownProps) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useClickAway(dropdownRef, () => {
    if (open) {
      setOpen(false);
    }
  });

  // Focus input when dropdown opens
  useEffect(() => {
    if (open && inputRef.current) {
      inputRef.current.focus();
    }
  }, [open]);

  // Filter options based on search query
  const filteredOptions = options.filter(option => 
    option.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle option selection
  const handleSelect = (selectedValue: string) => {
    onChange(selectedValue);
    setOpen(false);
    setSearchQuery('');
  };

  // Get selected option
  const selectedOption = options.find(option => option.value === value);

  return (
    <div 
      ref={dropdownRef}
      className={cn(
        "relative w-full",
        disabled && "opacity-50 cursor-not-allowed"
      )}
    >
      {/* Trigger button */}
      <div 
        className={cn(
          "w-full bg-white rounded-2xl p-3 transition-all cursor-pointer",
          isActive ? "ring-2 ring-[#DC2626]" : "hover:bg-gray-50",
          open && "ring-2 ring-[#DC2626]",
          disabled && "pointer-events-none"
        )}
        onClick={() => {
          if (!disabled) {
            onFocus();
            setOpen(!open);
          }
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 truncate">
            {selectedOption ? (
              renderOption ? (
                renderOption(selectedOption)
              ) : (
                <span className="text-sm">{selectedOption.label}</span>
              )
            ) : (
              <span className="text-sm text-gray-500">{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
        </div>
      </div>

      {/* Dropdown */}
      <AnimatePresence>
        {open && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.15 }}
            className="absolute z-50 w-full mt-1 bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
            style={{ minWidth: '240px' }}
          >
            <div className="p-2">
              {/* Search input */}
              <div className="relative mb-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={searchPlaceholder}
                  className="w-full px-3 py-2 text-sm bg-gray-100 border-none rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DC2626]"
                />
              </div>

              {/* Options list */}
              <div className="max-h-60 overflow-y-auto">
                {filteredOptions.length === 0 ? (
                  <div className="px-3 py-2 text-sm text-gray-500">{emptyMessage}</div>
                ) : (
                  <div className="space-y-1">
                    {filteredOptions.map((option) => (
                      <div
                        key={option.value}
                        className={cn(
                          "px-3 py-2 text-sm rounded-lg cursor-pointer flex items-center justify-between",
                          value === option.value ? "bg-[#DC2626]/10 text-[#DC2626]" : "hover:bg-gray-100"
                        )}
                        onClick={() => handleSelect(option.value)}
                      >
                        <div className="flex items-center">
                          {renderOption ? renderOption(option) : option.label}
                        </div>
                        {value === option.value && (
                          <Check className="h-4 w-4 text-[#DC2626]" />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
