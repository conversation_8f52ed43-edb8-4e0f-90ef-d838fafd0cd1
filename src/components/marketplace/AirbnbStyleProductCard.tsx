import React, { useState, useEffect } from 'react';
import { Heart, ShoppingCart, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useCart } from '@/contexts/CartContext';
import { useToast } from '@/components/ui/use-toast';
import { TyreProduct, BrakeProduct } from '@/features/products/types/product.types';
import { getProductMarketplaceSection } from '@/utils/productUtils';
import { toggleWishlist, isProductInWishlist } from '@/services/wishlistService';
import { getProductReviewStats } from '@/services/reviewsService';

interface AirbnbStyleProductCardProps {
  product: TyreProduct | BrakeProduct;
  section: 'retail' | 'wholesale';
}

export function AirbnbStyleProductCard({ product, section }: AirbnbStyleProductCardProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isFavorite, setIsFavorite] = useState(false);
  const [isWishlistLoading, setIsWishlistLoading] = useState(false);
  const [reviewStats, setReviewStats] = useState({ averageRating: 0, totalReviews: 0 });
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);
  const [pendingWishlistAction, setPendingWishlistAction] = useState(false);
  const { addItem } = useCart();

  // Check consumer authentication and load wishlist status
  useEffect(() => {
    const checkAuthAndWishlist = async () => {
      try {
        // Check authentication
        const phoneSession = localStorage.getItem('phone_auth_session');
        const isAuth = phoneSession && JSON.parse(phoneSession).profile?.role === 'consumer';
        setIsConsumerAuthenticated(!!isAuth);

        // Load wishlist status if authenticated
        if (isAuth) {
          const result = await isProductInWishlist(product.id);
          if (result.success) {
            setIsFavorite(result.isInWishlist || false);
          }
        }
      } catch (error) {
        console.error('Error checking auth/wishlist:', error);
      }
    };

    checkAuthAndWishlist();
  }, [product.id]);

  // Load review statistics
  useEffect(() => {
    const loadReviewStats = async () => {
      try {
        const result = await getProductReviewStats(product.id);
        if (result.success && result.stats) {
          setReviewStats({
            averageRating: result.stats.average_rating,
            totalReviews: result.stats.total_reviews
          });
        }
      } catch (error) {
        console.error('Error loading review stats:', error);
      }
    };

    loadReviewStats();
  }, [product.id]);

  // Listen for consumer authentication success to retry wishlist action
  useEffect(() => {
    const handleConsumerAuthSuccess = async () => {
      if (pendingWishlistAction && isConsumerAuthenticated) {
        setPendingWishlistAction(false);
        // Retry the wishlist action after successful authentication
        await performWishlistToggle();
      }
    };

    window.addEventListener('consumer-auth-success', handleConsumerAuthSuccess);

    return () => {
      window.removeEventListener('consumer-auth-success', handleConsumerAuthSuccess);
    };
  }, [pendingWishlistAction, isConsumerAuthenticated]);

  // Helper function to convert product ID string to number for cart (IDENTICAL to ProductPage)
  const getCartItemId = (productId: string): number => {
    // Simple hash function to convert string ID to number
    let hash = 0;
    for (let i = 0; i < productId.length; i++) {
      const char = productId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  };

  // Format price with Algerian Dinar
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Perform wishlist toggle action (extracted for reuse after authentication)
  const performWishlistToggle = async () => {
    setIsWishlistLoading(true);

    try {
      const result = await toggleWishlist({
        product_id: product.id,
        product_name: product.name,
        product_image: product.primaryImage,
        product_price: getProductPrice(),
        product_manufacturer: product.manufacturer || product.brand
      });

      if (result.success) {
        const newFavoriteState = result.action === 'added';
        setIsFavorite(newFavoriteState);

        toast.success(
          newFavoriteState ? t('marketplace.addedToWishlist') : t('marketplace.removedFromWishlist'),
          {
            description: newFavoriteState
              ? t('marketplace.addedToWishlistDescription')
              : t('marketplace.removedFromWishlistDescription')
          }
        );

        // Dispatch event to update wishlist count in header
        window.dispatchEvent(new CustomEvent('wishlist:updated'));
      } else {
        toast.error(t('marketplace.wishlistError'), {
          description: result.error || t('marketplace.wishlistErrorDescription')
        });
      }
    } catch (error) {
      console.error('Error toggling wishlist:', error);
      toast.error(t('marketplace.wishlistError'), {
        description: t('marketplace.wishlistErrorDescription')
      });
    } finally {
      setIsWishlistLoading(false);
    }
  };

  // Toggle favorite status with authentication check
  const toggleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Check if user is authenticated
    if (!isConsumerAuthenticated) {
      // Set pending action and trigger auth modal for seamless experience
      setPendingWishlistAction(true);
      const authModal = document.getElementById('auth-modal-trigger');
      if (authModal) {
        authModal.click();
      }
      return;
    }

    // User is authenticated, perform the action immediately
    await performWishlistToggle();
  };

  // Navigate to product page
  const handleProductClick = () => {
    navigate(`/${product.id}`);
  };

  // Add to cart
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Use effective minimum order quantity for wholesale products
    const effectiveQuantity = section === 'wholesale' ? (getMinOrderQuantity() || 1) : 1;

    // Log product data before adding to cart
    console.log('🛒 [MARKETPLACE_CARD] Adding product to cart:', {
      id: product.id,
      name: product.name,
      supplierName: product.supplierName,
      supplierAccountId: product.supplierAccountId,
      shippingOrigin: product.shippingOrigin,
      rawProduct: product
    });

    const cartItem = {
      id: getCartItemId(product.id),
      name: product.name,
      price: section === 'retail' ? (product.retailPrice || 0) : (product.wholesalePricingTiers?.[0]?.price || 0),
      quantity: effectiveQuantity,
      image: product.primaryImage || '/placeholder.svg',
      // Complete product metadata for proper persistence
      category: product.category,
      subcategory: product.subcategory,
      manufacturer: product.manufacturer,
      brand: (product as any).brand, // Some products might have brand field
      partArticleNumber: product.partArticleNumber,
      supplierName: product.supplierName,
      supplierAccountId: product.supplierAccountId, // CRITICAL: Supplier account ID for shipping companies
      originalProductId: product.id,
      // Shipping & Location Information
      shippingOrigin: product.shippingOrigin || 'Location TBD', // CRITICAL: Supplier location for shipping origins
      // Wholesale pricing support
      marketplaceSection: section,
      wholesalePricingTiers: product.wholesalePricingTiers,
      retailPrice: product.retailPrice
    };

    console.log('🛒 [MARKETPLACE_CARD] Final cart item being added:', cartItem);
    addItem(cartItem);
  };

  // Determine if product is out of stock
  const isOutOfStock = product.status === 'out_of_stock' || product.stockQuantity <= 0;

  // Get product specifications based on category
  const getProductSpecs = () => {
    if (product.category === 'tyres') {
      const tyreProduct = product as TyreProduct;
      if (tyreProduct.width && tyreProduct.aspectRatio && tyreProduct.rimDiameter) {
        return `${tyreProduct.width}/${tyreProduct.aspectRatio}R${tyreProduct.rimDiameter}`;
      }
    }
    return product.subcategory || '';
  };

  // Get product price based on section (retail or wholesale)
  const getProductPrice = () => {
    if (section === 'retail') {
      return product.retailPrice || 0;
    } else {
      // For wholesale, use the first tier price
      return product.wholesalePricingTiers?.[0]?.price || 0;
    }
  };

  // Get effective minimum order quantity for wholesale products (IDENTICAL to ProductPage logic)
  const getMinOrderQuantity = () => {
    if (section === 'wholesale') {
      // Calculate effective minimum order quantity - derive from wholesale tiers if undefined
      const effectiveMinimumOrderQuantity = product.minimumOrderQuantity ||
        (product.wholesalePricingTiers?.[0]?.minQuantity) ||
        1;

      console.log('🔍 [MARKETPLACE_CARD_MIN_QUANTITY] Calculating for:', {
        productId: product.id,
        section,
        minimumOrderQuantity: product.minimumOrderQuantity,
        wholesalePricingTiersFirstMinQuantity: product.wholesalePricingTiers?.[0]?.minQuantity,
        effectiveMinimumOrderQuantity
      });

      return effectiveMinimumOrderQuantity;
    }
    return null;
  };

  return (
    <div
      className="group cursor-pointer transition-all duration-200 hover:scale-[1.02]"
      onClick={handleProductClick}
    >
      {/* Favorite button */}
      <button
        className={cn(
          "absolute top-3 right-3 z-10 rounded-full p-1.5 backdrop-blur-sm transition-all duration-200",
          isFavorite
            ? "bg-white/90 text-red-500 shadow-md"
            : "bg-white/70 text-gray-600 hover:bg-white/90 hover:text-red-500 shadow-sm",
          isWishlistLoading && "opacity-50 cursor-not-allowed"
        )}
        onClick={toggleFavorite}
        disabled={isWishlistLoading}
        aria-label={isFavorite ? t('marketplace.removeFromWishlist') : t('marketplace.addToWishlist')}
      >
        <Heart className={cn(
          "h-4 w-4 transition-all duration-200",
          isFavorite && "fill-current scale-110",
          isWishlistLoading && "animate-pulse"
        )} />
      </button>

      {/* Product image */}
      <div className="aspect-square overflow-hidden relative rounded-xl mb-3">
        <img
          src={product.primaryImage || '/placeholder.svg'}
          alt={product.name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />

        {/* Discount badge */}
        {section === 'wholesale' && (
          <div className="absolute top-3 left-3">
            <Badge className="bg-[#fa7b00] text-white text-xs font-bold px-2 py-1">
              -{Math.floor(Math.random() * 30 + 10)}%
            </Badge>
          </div>
        )}

        {/* Out of stock overlay */}
        {isOutOfStock && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center rounded-xl">
            <Badge variant="destructive" className="text-sm px-3 py-1 font-medium">
              {t('marketplace.outOfStock')}
            </Badge>
          </div>
        )}
      </div>

      {/* Product details - Airbnb style */}
      <div className="space-y-2">
        {/* Location/Brand and Rating */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-900 truncate">
            {product.manufacturer || product.brand || 'Premium Brand'}
          </span>
          <button
            className="flex items-center gap-1 hover:opacity-75 transition-opacity"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              navigate(`/${product.id}#reviews-section`);
            }}
          >
            <Star className="h-3 w-3 fill-black text-black" />
            <span className="text-sm font-medium">
              {reviewStats.totalReviews > 0
                ? reviewStats.averageRating.toFixed(1)
                : 'New'
              }
            </span>
            {reviewStats.totalReviews > 0 && (
              <span className="text-xs text-gray-500 ml-1">
                ({reviewStats.totalReviews})
              </span>
            )}
          </button>
        </div>

        {/* Product name - clean and minimal */}
        <h3 className="text-sm text-gray-600 line-clamp-1">
          {product.name}
        </h3>

        {/* Part Article Number - Universal field */}
        <div className="text-xs text-gray-500 space-y-1">
          {product.partArticleNumber && (
            <div>Part No.: {product.partArticleNumber}</div>
          )}
          {product.supplierName && (
            <div className="truncate">Supplier: {product.supplierName}</div>
          )}
          {getProductSpecs() && (
            <div className="truncate">{getProductSpecs()}</div>
          )}
        </div>

        {/* Availability status */}
        <div className="text-xs">
          {isOutOfStock ? (
            <span className="text-red-600 font-medium">{t('marketplace.outOfStock')}</span>
          ) : (
            <span className="text-green-600 font-medium">✓ {t('marketplace.inStock')}</span>
          )}
        </div>

        {/* Minimum order for wholesale */}
        {section === 'wholesale' && getMinOrderQuantity() && (
          <div className="text-xs text-gray-500">
            {t('marketplace.minOrder')}: {getMinOrderQuantity()} {t('marketplace.units')}
          </div>
        )}

        {/* Price - Airbnb style */}
        <div className="pt-1">
          <div className="flex items-baseline gap-1">
            <span className="text-base font-semibold text-gray-900">
              {formatPrice(getProductPrice())}
            </span>
            {section === 'retail' && (
              <span className="text-sm text-gray-600">/ {t('marketplace.piece')}</span>
            )}
          </div>

          {/* Add to cart button - more subtle */}
          <Button
            size="sm"
            className={cn(
              "w-full mt-3 transition-all duration-200",
              "bg-[#DC2626] hover:bg-[#B91C1C] text-white",
              "hover:shadow-md active:scale-95"
            )}
            onClick={handleAddToCart}
            disabled={isOutOfStock}
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            {isOutOfStock ? t('marketplace.outOfStock') : t('marketplace.addToCart')}
          </Button>
        </div>
      </div>
    </div>
  );
}
