import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search, MapPin } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TyresSearchProps {
  isCompact?: boolean;
}

export function TyresSearch({ isCompact = false }: TyresSearchProps) {
  const { t } = useTranslation();
  const [location, setLocation] = useState('');
  const [width, setWidth] = useState('');
  const [aspectRatio, setAspectRatio] = useState('');
  const [rimDiameter, setRimDiameter] = useState('');
  const [season, setSeason] = useState('');

  const handleSearch = () => {
    console.log('Searching for tyres with params:', { width, aspectRatio, rimDiameter, season, location });
    // In a real app, this would navigate to search results or trigger an API call
  };

  return (
    <div className={cn(
      "flex flex-col transition-all duration-300",
      isCompact ? "gap-3" : "gap-4"
    )}>
      <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
        {/* Width */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="tyre-width"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            {t('marketplace.tyreWidth')}
          </Label>
          <Select value={width} onValueChange={setWidth}>
            <SelectTrigger
              id="tyre-width"
              className={cn(
                "w-full border-gray-300 focus:ring-[#DC2626] focus:border-[#DC2626] rounded-lg transition-all duration-300",
                isCompact ? "h-9 text-sm" : "h-10"
              )}
            >
              <SelectValue placeholder={t('marketplace.selectWidth')} />
            </SelectTrigger>
            <SelectContent className="max-h-[300px]">
              {[155, 165, 175, 185, 195, 205, 215, 225, 235, 245, 255, 265, 275, 285, 295, 305, 315, 325].map((w) => (
                <SelectItem key={w} value={w.toString()}>
                  {w} mm
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Aspect Ratio */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="tyre-aspect-ratio"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            {t('marketplace.aspectRatio')}
          </Label>
          <Select value={aspectRatio} onValueChange={setAspectRatio}>
            <SelectTrigger
              id="tyre-aspect-ratio"
              className={cn(
                "w-full border-gray-300 focus:ring-[#DC2626] focus:border-[#DC2626] rounded-lg transition-all duration-300",
                isCompact ? "h-9 text-sm" : "h-10"
              )}
            >
              <SelectValue placeholder={t('marketplace.selectRatio')} />
            </SelectTrigger>
            <SelectContent className="max-h-[300px]">
              {[30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85].map((ratio) => (
                <SelectItem key={ratio} value={ratio.toString()}>
                  {ratio}%
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Rim Diameter */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="tyre-rim-diameter"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            {t('marketplace.rimDiameter')}
          </Label>
          <Select value={rimDiameter} onValueChange={setRimDiameter}>
            <SelectTrigger
              id="tyre-rim-diameter"
              className={cn(
                "w-full border-gray-300 focus:ring-[#DC2626] focus:border-[#DC2626] rounded-lg transition-all duration-300",
                isCompact ? "h-9 text-sm" : "h-10"
              )}
            >
              <SelectValue placeholder={t('marketplace.selectDiameter')} />
            </SelectTrigger>
            <SelectContent className="max-h-[300px]">
              {[13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24].map((diameter) => (
                <SelectItem key={diameter} value={diameter.toString()}>
                  {diameter}"
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Season */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="tyre-season"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            {t('marketplace.season')}
          </Label>
          <Select value={season} onValueChange={setSeason}>
            <SelectTrigger
              id="tyre-season"
              className={cn(
                "w-full border-gray-300 focus:ring-[#DC2626] focus:border-[#DC2626] rounded-lg transition-all duration-300",
                isCompact ? "h-9 text-sm" : "h-10"
              )}
            >
              <SelectValue placeholder={t('marketplace.selectSeason')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="summer">{t('marketplace.summer')}</SelectItem>
              <SelectItem value="winter">{t('marketplace.winter')}</SelectItem>
              <SelectItem value="all-season">{t('marketplace.allSeason')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Delivery Location */}
        <div className={cn(
          "space-y-1.5 transition-all duration-300",
          isCompact ? "space-y-1" : "space-y-1.5"
        )}>
          <Label
            htmlFor="delivery-location"
            className={cn(
              "text-gray-700 font-medium transition-all duration-300",
              isCompact ? "text-xs" : "text-sm"
            )}
          >
            {t('marketplace.deliverTo')}
          </Label>
          <div className="relative">
            <MapPin
              className={cn(
                "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 transition-all duration-300",
                isCompact ? "h-4 w-4" : "h-5 w-5"
              )}
            />
            <Input
              id="delivery-location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder={t('marketplace.enterLocation')}
              className={cn(
                "pl-10 border-gray-300 focus:ring-[#DC2626] focus:border-[#DC2626] rounded-lg transition-all duration-300",
                isCompact ? "h-9 text-sm" : "h-10"
              )}
            />
          </div>
        </div>
      </div>

      {/* Search Button */}
      <div className="flex justify-center mt-2">
        <Button
          onClick={handleSearch}
          className={cn(
            "bg-[#DC2626] hover:bg-[#DC2626]/90 text-white font-medium rounded-full transition-all duration-300",
            isCompact
              ? "px-6 py-1.5 text-sm h-9"
              : "px-8 py-2 text-base h-10"
          )}
        >
          <Search className={cn("mr-2 transition-all duration-300", isCompact ? "h-4 w-4" : "h-5 w-5")} />
          {t('marketplace.searchTyres')}
        </Button>
      </div>
    </div>
  );
}
