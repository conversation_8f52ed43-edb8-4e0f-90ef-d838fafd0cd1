/**
 * Product Reviews Component - Display and Manage Product Reviews
 * 
 * Shows product reviews with ratings, pagination, and review submission
 * 🎯 CRITICAL: Preserves existing authentication system - integrates with phone auth
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
  Star,
  MessageSquare,
  ThumbsUp,
  Calendar,
  User,
  Edit3
} from 'lucide-react';
import { 
  getProductReviews, 
  getProductReviewStats, 
  hasConsumerReviewedProduct,
  ConsumerReview,
  ReviewStats 
} from '@/services/reviewsService';
import { ReviewModal } from './ReviewModal';

interface ProductReviewsProps {
  productId: string;
  productName: string;
}

export function ProductReviews({ productId, productName }: ProductReviewsProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  const [reviews, setReviews] = useState<ConsumerReview[]>([]);
  const [reviewStats, setReviewStats] = useState<ReviewStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConsumerAuthenticated, setIsConsumerAuthenticated] = useState(false);
  const [hasUserReviewed, setHasUserReviewed] = useState(false);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [displayCount, setDisplayCount] = useState(5);

  // Check authentication and load reviews
  useEffect(() => {
    const loadReviewsData = async () => {
      try {
        setIsLoading(true);
        
        // Check authentication
        const phoneSession = localStorage.getItem('phone_auth_session');
        const isAuth = phoneSession && JSON.parse(phoneSession).profile?.role === 'consumer';
        setIsConsumerAuthenticated(!!isAuth);

        // Load review statistics
        const statsResult = await getProductReviewStats(productId);
        if (statsResult.success) {
          setReviewStats(statsResult.stats || null);
        }

        // Load reviews
        const reviewsResult = await getProductReviews(productId);
        if (reviewsResult.success) {
          setReviews(reviewsResult.reviews || []);
        }

        // Check if user has already reviewed (if authenticated)
        if (isAuth) {
          const hasReviewedResult = await hasConsumerReviewedProduct(productId);
          if (hasReviewedResult.success) {
            setHasUserReviewed(hasReviewedResult.hasReviewed || false);
          }
        }
      } catch (error) {
        console.error('Error loading reviews:', error);
        toast.error('Failed to load reviews', {
          description: 'Please try again'
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadReviewsData();
  }, [productId, toast]);

  // Handle review submission with optimistic updates
  const handleReviewSubmitted = async (newReview?: ConsumerReview) => {
    // Immediately update user review status for instant feedback
    setHasUserReviewed(true);

    // If we have the new review data, add it optimistically
    if (newReview) {
      setReviews(prevReviews => [newReview, ...prevReviews]);

      // Update stats optimistically
      if (reviewStats) {
        const newTotal = reviewStats.total_reviews + 1;
        const newAverage = ((reviewStats.average_rating * reviewStats.total_reviews) + newReview.rating) / newTotal;
        const newDistribution = { ...reviewStats.rating_distribution };
        newDistribution[newReview.rating as keyof typeof newDistribution]++;

        setReviewStats({
          total_reviews: newTotal,
          average_rating: Math.round(newAverage * 10) / 10, // Round to 1 decimal
          rating_distribution: newDistribution
        });
      }
    }

    // Refresh data in background for accuracy (non-blocking)
    Promise.all([
      getProductReviews(productId),
      getProductReviewStats(productId)
    ]).then(([reviewsResult, statsResult]) => {
      if (reviewsResult.success) {
        setReviews(reviewsResult.reviews || []);
      }
      if (statsResult.success) {
        setReviewStats(statsResult.stats || null);
      }
    }).catch(error => {
      console.error('Error refreshing review data:', error);
      // Fallback: reload page data if background refresh fails
      window.location.reload();
    });
  };

  // Handle write review click
  const handleWriteReviewClick = () => {
    if (!isConsumerAuthenticated) {
      toast.error(t('auth.loginRequired'), {
        description: t('auth.loginRequiredDescription'),
        action: {
          label: t('auth.login'),
          onClick: () => {
            const authModal = document.getElementById('auth-modal-trigger');
            if (authModal) {
              authModal.click();
            }
          }
        }
      });
      return;
    }

    setIsReviewModalOpen(true);
  };

  // Render star rating
  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const starSize = size === 'sm' ? 'h-4 w-4' : 'h-5 w-5';
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${starSize} ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'fill-gray-200 text-gray-200'
            }`}
          />
        ))}
      </div>
    );
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Render rating distribution
  const renderRatingDistribution = () => {
    if (!reviewStats || reviewStats.total_reviews === 0) return null;

    return (
      <div className="space-y-2">
        {[5, 4, 3, 2, 1].map((rating) => {
          const count = reviewStats.rating_distribution[rating as keyof typeof reviewStats.rating_distribution];
          const percentage = (count / reviewStats.total_reviews) * 100;
          
          return (
            <div key={rating} className="flex items-center gap-3 text-sm">
              <span className="w-8 text-right">{rating}</span>
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span className="w-8 text-gray-600">{count}</span>
            </div>
          );
        })}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid md:grid-cols-3 gap-6">
          <Skeleton className="h-48" />
          <div className="md:col-span-2 space-y-4">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-32" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
          <MessageSquare className="h-6 w-6 text-[#DC2626]" />
          {t('marketplace.customerReviews')}
          {reviewStats && reviewStats.total_reviews > 0 && (
            <span className="text-lg text-gray-600">
              ({reviewStats.total_reviews})
            </span>
          )}
        </h3>
        
        {!hasUserReviewed && (
          <Button
            onClick={handleWriteReviewClick}
            className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white"
          >
            <Edit3 className="h-4 w-4 mr-2" />
            {t('marketplace.writeReview')}
          </Button>
        )}
      </div>

      {reviewStats && reviewStats.total_reviews > 0 ? (
        <div className="grid md:grid-cols-3 gap-6">
          {/* Rating Summary */}
          <Card>
            <CardHeader className="text-center">
              <div className="text-4xl font-bold text-gray-900">
                {reviewStats.average_rating.toFixed(1)}
              </div>
              <div className="flex justify-center mb-2">
                {renderStars(Math.round(reviewStats.average_rating), 'md')}
              </div>
              <p className="text-sm text-gray-600">
                Based on {reviewStats.total_reviews} review{reviewStats.total_reviews !== 1 ? 's' : ''}
              </p>
            </CardHeader>
            <CardContent>
              {renderRatingDistribution()}
            </CardContent>
          </Card>

          {/* Reviews List */}
          <div className="md:col-span-2 space-y-4">
            {reviews.slice(0, displayCount).map((review) => (
              <Card key={review.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-[#DC2626] rounded-full flex items-center justify-center text-white font-bold">
                        {review.consumer_name?.charAt(0).toUpperCase() || 'U'}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {review.consumer_name || 'Anonymous'}
                        </p>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Calendar className="h-3 w-3" />
                          {formatDate(review.created_at)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {renderStars(review.rating)}
                      <span className="text-sm text-gray-600">
                        {review.rating}/5
                      </span>
                    </div>
                  </div>

                  {review.title && (
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {review.title}
                    </h4>
                  )}

                  {review.review_text && (
                    <p className="text-gray-700 leading-relaxed mb-3">
                      {review.review_text}
                    </p>
                  )}

                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    {review.is_verified_purchase && (
                      <Badge variant="secondary" className="text-xs">
                        Verified Purchase
                      </Badge>
                    )}
                    {review.helpful_count > 0 && (
                      <div className="flex items-center gap-1">
                        <ThumbsUp className="h-3 w-3" />
                        {review.helpful_count} helpful
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Load More Button */}
            {reviews.length > displayCount && (
              <div className="text-center">
                <Button
                  variant="outline"
                  onClick={() => setDisplayCount(prev => prev + 5)}
                >
                  Load More Reviews
                </Button>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <MessageSquare className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {t('marketplace.noReviewsYet')}
          </h3>
          <p className="text-gray-600 mb-6">
            {t('marketplace.beFirstToReview')}
          </p>
          {!hasUserReviewed && (
            <Button
              onClick={handleWriteReviewClick}
              className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white"
            >
              <Edit3 className="h-4 w-4 mr-2" />
              {t('marketplace.writeFirstReview')}
            </Button>
          )}
        </div>
      )}

      {/* Review Modal */}
      <ReviewModal
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        productId={productId}
        productName={productName}
        onReviewSubmitted={handleReviewSubmitted}
      />
    </div>
  );
}
