import React, { useState, useRef } from 'react';
import { MapPin, ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { useClickAway } from 'react-use';
import { motion, AnimatePresence } from 'framer-motion';

interface DeliveryLocationButtonProps {
  location: string;
  onChange: (location: string) => void;
}

export function DeliveryLocationButton({ location, onChange }: DeliveryLocationButtonProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(location);
  const buttonRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useClickAway(buttonRef, () => {
    if (isOpen) {
      setIsOpen(false);
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onChange(inputValue);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={buttonRef}>
      {/* Delivery Location Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center bg-white hover:bg-gray-50 rounded-full border border-gray-200 py-2 px-3.5 shadow-sm transition-colors"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <MapPin className="h-5 w-5 text-[#DC2626] mr-2 flex-shrink-0" />
        <div className="flex flex-col items-start">
          <span className="text-xs text-gray-500 leading-tight">
            {t('marketplace.deliverTo')}
          </span>
          <span className="text-sm font-medium text-gray-900 truncate max-w-[120px]">
            {location || t('marketplace.selectLocation')}
          </span>
        </div>
        <ChevronDown
          className={cn(
            "ml-1.5 h-4 w-4 text-gray-500 transition-transform",
            isOpen && "transform rotate-180"
          )}
        />
      </button>

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.15 }}
            className="absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
          >
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-3">
                {t('marketplace.chooseYourLocation')}
              </h3>
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="delivery-location" className="block text-sm text-gray-700 mb-1">
                    {t('marketplace.enterDeliveryLocation')}
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      id="delivery-location"
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder={t('marketplace.enterLocation')}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-[#DC2626] focus:border-[#DC2626]"
                    />
                  </div>
                </div>
                <div className="flex justify-end">
                  <button
                    type="button"
                    className="mr-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    {t('common.cancel')}
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm bg-[#DC2626] hover:bg-[#DC2626]/90 text-white rounded-md transition-colors"
                  >
                    {t('common.apply')}
                  </button>
                </div>
              </form>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
