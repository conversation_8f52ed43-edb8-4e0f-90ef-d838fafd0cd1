import React, { useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Search, X, Car } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { useClickAway } from 'react-use';
import { useIsMobile } from '@/hooks/use-mobile';
import { TyreIcon } from './icons/TyreIcon';
import { AirbnbStyleFilterModal } from './AirbnbStyleFilterModal';
import { useFilter } from '@/contexts/FilterContext';
import { useToast } from '@/hooks/use-toast';

interface AirbnbStyleSearchBarProps {
  onSearch: (params: any) => void;
}

export function AirbnbStyleSearchBar({ onSearch }: AirbnbStyleSearchBarProps) {
  const { t } = useTranslation();
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const [mobileSearchExpanded, setMobileSearchExpanded] = useState(false);
  const [searchText, setSearchText] = useState('');

  // Use the filter context instead of local state
  const {
    tyresFilters,
    setTyresFilters,
    partsFilters,
    setPartsFilters,
    tyresFilterCount,
    partsFilterCount,
    showTyresModal,
    setShowTyresModal,
    showPartsModal,
    setShowPartsModal
  } = useFilter();

  const searchBarRef = useRef<HTMLDivElement>(null);

  // Handle search submission
  const handleSearch = () => {
    // Search across all products with both filters applied
    onSearch({
      ...tyresFilters,
      ...partsFilters,
      searchText
    });

    // Close mobile search after submitting
    if (isMobile) {
      setMobileSearchExpanded(false);
    }
  };

  // Filter submissions are now handled by MarketplaceHeader

  // Open tyre filter modal with coming soon message
  const handleOpenTyresModal = () => {
    toast({
      title: t('marketplace.comingSoon'),
      description: t('marketplace.underDevelopment'),
      duration: 3000,
    });
    // Keep the original functionality for now
    // setShowTyresModal(true);
  };

  // Open parts filter modal with coming soon message
  const handleOpenPartsModal = () => {
    toast({
      title: t('marketplace.comingSoon'),
      description: t('marketplace.underDevelopment'),
      duration: 3000,
    });
    // Keep the original functionality for now
    // setShowPartsModal(true);
  };

  const handleMobileSearchToggle = () => {
    setMobileSearchExpanded(!mobileSearchExpanded);
  };

  return (
    <div className="relative z-50 w-full" ref={searchBarRef}>
      {/* Main search bar container */}
      <div className="mx-auto w-full">
        {/* Desktop Search Bar */}
        {!isMobile && (
          <div
            className="bg-white rounded-full shadow-md border border-gray-200 transition-all duration-300 hover:shadow-lg"
          >
            {/* Search bar */}
            <div className="flex items-center h-16 px-2">
              {/* Filter buttons */}
              <div className="flex items-center px-4 border-r border-gray-200">
                <div className="flex items-center space-x-3 bg-gray-100 rounded-full p-1.5">
                  <button
                    className="px-5 py-2 rounded-full text-base font-medium transition-all relative bg-gray-200 text-gray-700 hover:bg-[#DC2626] hover:text-white"
                    onClick={handleOpenTyresModal}
                  >
                    <span className="flex items-center">
                      <TyreIcon className="w-5 h-5 mr-2 text-gray-600" />
                      {t('marketplace.tyres')}

                      {/* Badge for filter count - always visible */}
                      <span className={`absolute -top-2 -right-2 ${tyresFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center`}>
                        {tyresFilterCount}
                      </span>
                    </span>
                  </button>
                  <button
                    className="px-5 py-2 rounded-full text-base font-medium transition-all relative bg-gray-200 text-gray-700 hover:bg-[#DC2626] hover:text-white"
                    onClick={handleOpenPartsModal}
                  >
                    <span className="flex items-center">
                      <Car className="w-5 h-5 mr-2 text-gray-600" />
                      {t('marketplace.allOtherParts')}

                      {/* Badge for filter count - always visible */}
                      <span className={`absolute -top-2 -right-2 ${partsFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center`}>
                        {partsFilterCount}
                      </span>
                    </span>
                  </button>
                </div>
              </div>

              {/* Text search input */}
              <div className="flex-1 px-4 py-2">
                <input
                  type="text"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  placeholder={t('marketplace.searchAllProducts')}
                  className="w-full bg-transparent border-none p-0 text-base focus:outline-none focus:ring-0"
                />
              </div>

              {/* Search button */}
              <div className="pr-2">
                <button
                  className="bg-[#DC2626] hover:bg-[#DC2626]/90 text-white p-3 rounded-full transition-all"
                  onClick={handleSearch}
                >
                  <Search className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Mobile Search Bar */}
        {isMobile && (
          <div className="bg-white rounded-full shadow-md border border-gray-200">
            {/* Collapsed mobile search bar */}
            <div
              className="flex items-center h-12 px-4 cursor-pointer"
              onClick={handleMobileSearchToggle}
            >
              <Search className="w-4 h-4 text-gray-500 mr-2" />
              <div className="flex-1 text-sm text-gray-500">
                {t('marketplace.startYourSearch')}
              </div>

              {/* Show filter count badges - always visible */}
              <span className={`${tyresFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center ml-2`}>
                {tyresFilterCount}
              </span>

              <span className={`${partsFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center ml-2`}>
                {partsFilterCount}
              </span>
            </div>
          </div>
        )}

        {/* Filter Modals are now managed by MarketplaceHeader */}

        {/* Mobile Expanded Search Interface */}
        <AnimatePresence>
          {isMobile && mobileSearchExpanded && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-white z-50 overflow-y-auto"
            >
              {/* Mobile search header */}
              <div className="flex items-center justify-between p-4 border-b">
                <button
                  className="text-gray-500"
                  onClick={handleMobileSearchToggle}
                >
                  <X className="h-6 w-6" />
                </button>
                <div className="text-base font-medium">
                  {t('marketplace.search')}
                </div>
                <div className="w-6"></div> {/* Empty div for alignment */}
              </div>

              {/* Filter buttons */}
              <div className="p-4 border-b">
                <div className="flex justify-center">
                  <div className="flex items-center space-x-3 bg-gray-100 rounded-full p-1.5 w-full max-w-xs">
                    <button
                      className="flex-1 py-2.5 rounded-full text-base font-medium transition-all text-center relative bg-gray-200 text-gray-700 hover:bg-[#DC2626] hover:text-white"
                      onClick={handleOpenTyresModal}
                    >
                      <span className="flex items-center justify-center">
                        <TyreIcon className="w-5 h-5 mr-2 text-gray-600" />
                        {t('marketplace.tyres')}
                      </span>

                      {/* Badge for filter count - always visible */}
                      <span className={`absolute -top-2 -right-2 ${tyresFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center`}>
                        {tyresFilterCount}
                      </span>
                    </button>
                    <button
                      className="flex-1 py-2.5 rounded-full text-base font-medium transition-all text-center relative bg-gray-200 text-gray-700 hover:bg-[#DC2626] hover:text-white"
                      onClick={handleOpenPartsModal}
                    >
                      <span className="flex items-center justify-center">
                        <Car className="w-5 h-5 mr-2 text-gray-600" />
                        {t('marketplace.allOtherParts')}
                      </span>

                      {/* Badge for filter count - always visible */}
                      <span className={`absolute -top-2 -right-2 ${partsFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center`}>
                        {partsFilterCount}
                      </span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Mobile search fields */}
              <div className="p-4 space-y-4">
                {/* Text search input */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">{t('marketplace.searchText')}</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      placeholder={t('marketplace.searchAllProducts')}
                      className="w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-[#DC2626] focus:border-[#DC2626]"
                    />
                  </div>
                </div>

                {/* Filter buttons */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">{t('marketplace.filters')}</label>
                  <div className="flex flex-col gap-2">
                    <button
                      className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 px-4 rounded-lg transition-all flex items-center justify-between"
                      onClick={handleOpenTyresModal}
                    >
                      <span>
                        {t('marketplace.setTyreFilters')}
                      </span>
                      <span className="flex items-center">
                        <span className={`${tyresFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center mr-2`}>
                          {tyresFilterCount}
                        </span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                      </span>
                    </button>

                    <button
                      className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-3 px-4 rounded-lg transition-all flex items-center justify-between"
                      onClick={handleOpenPartsModal}
                    >
                      <span>
                        {t('marketplace.setVehicleFilters')}
                      </span>
                      <span className="flex items-center">
                        <span className={`${partsFilterCount > 0 ? 'bg-blue-500' : 'bg-gray-400'} text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center mr-2`}>
                          {partsFilterCount}
                        </span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                        </svg>
                      </span>
                    </button>
                  </div>
                </div>

                {/* Search button */}
                <div className="pt-4">
                  <button
                    className="w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white py-3 rounded-lg transition-all flex items-center justify-center"
                    onClick={handleSearch}
                  >
                    <Search className="w-5 h-5 mr-2" />
                    {t('marketplace.search')}
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}


