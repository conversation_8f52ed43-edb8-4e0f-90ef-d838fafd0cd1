/**
 * Dashboard Metric Card Component
 * 
 * Reusable metric card component following AROUZ MARKET design system
 * with animations, loading states, and responsive design
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DashboardMetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: LucideIcon;
  trend?: {
    value: number;
    label: string;
    direction: 'up' | 'down' | 'neutral';
  };
  loading?: boolean;
  className?: string;
  variant?: 'default' | 'revenue' | 'orders' | 'products' | 'customers';
  onClick?: () => void;
}

const variantStyles = {
  default: {
    card: 'border-gray-200 hover:border-gray-300',
    icon: 'text-gray-600',
    background: 'bg-gray-50',
  },
  revenue: {
    card: 'border-green-200 hover:border-green-300 hover:shadow-green-100',
    icon: 'text-green-600',
    background: 'bg-green-50',
  },
  orders: {
    card: 'border-[#DC2626]/20 hover:border-[#DC2626]/40 hover:shadow-red-100',
    icon: 'text-[#DC2626]',
    background: 'bg-[#DC2626]/10',
  },
  products: {
    card: 'border-blue-200 hover:border-blue-300 hover:shadow-blue-100',
    icon: 'text-blue-600',
    background: 'bg-blue-50',
  },
  customers: {
    card: 'border-[#071c44]/20 hover:border-[#071c44]/40 hover:shadow-blue-100',
    icon: 'text-[#071c44]',
    background: 'bg-[#071c44]/5',
  },
};

export function DashboardMetricCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  loading = false,
  className,
  variant = 'default',
  onClick,
}: DashboardMetricCardProps) {
  const styles = variantStyles[variant];

  const formatValue = (val: string | number): string => {
    if (typeof val === 'number') {
      // Format large numbers
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      } else if (val % 1 !== 0) {
        return val.toFixed(2);
      }
      return val.toString();
    }
    return val;
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    
    switch (trend.direction) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />;
      case 'down':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return <Minus className="h-3 w-3" />;
    }
  };

  const getTrendColor = () => {
    if (!trend) return '';
    
    switch (trend.direction) {
      case 'up':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'down':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <Card className={cn('transition-all duration-200', styles.card, className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
          <div className={cn('h-8 w-8 rounded-full animate-pulse', styles.background)} />
        </CardHeader>
        <CardContent>
          <div className="h-8 w-16 bg-gray-200 rounded animate-pulse mb-2" />
          <div className="h-3 w-20 bg-gray-200 rounded animate-pulse" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      className={cn(
        'transition-all duration-200 hover:shadow-md',
        styles.card,
        onClick && 'cursor-pointer hover:scale-[1.02]',
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-700">
          {title}
        </CardTitle>
        {Icon && (
          <div className={cn('p-2 rounded-full', styles.background)}>
            <Icon className={cn('h-4 w-4', styles.icon)} />
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline justify-between">
          <div>
            <div className="text-2xl font-bold text-gray-900">
              {formatValue(value)}
            </div>
            {subtitle && (
              <p className="text-xs text-gray-600 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {trend && (
            <Badge 
              variant="outline" 
              className={cn(
                'flex items-center gap-1 text-xs font-medium',
                getTrendColor()
              )}
            >
              {getTrendIcon()}
              {trend.value > 0 && '+'}
              {trend.value}%
            </Badge>
          )}
        </div>
        {trend && trend.label && (
          <p className="text-xs text-gray-500 mt-2">
            {trend.label}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Revenue Metric Card - Specialized for revenue display
 */
export function RevenueMetricCard({
  title,
  value,
  currency = 'DZD',
  ...props
}: Omit<DashboardMetricCardProps, 'value'> & {
  value: number;
  currency?: string;
}) {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-DZ', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <DashboardMetricCard
      {...props}
      title={title}
      value={formatCurrency(value)}
      variant="revenue"
    />
  );
}

/**
 * Percentage Metric Card - Specialized for percentage display
 */
export function PercentageMetricCard({
  value,
  ...props
}: Omit<DashboardMetricCardProps, 'value'> & {
  value: number;
}) {
  return (
    <DashboardMetricCard
      {...props}
      value={`${value.toFixed(1)}%`}
    />
  );
}

/**
 * Count Metric Card - Specialized for count display
 */
export function CountMetricCard({
  value,
  ...props
}: Omit<DashboardMetricCardProps, 'value'> & {
  value: number;
}) {
  return (
    <DashboardMetricCard
      {...props}
      value={value.toLocaleString()}
    />
  );
}
