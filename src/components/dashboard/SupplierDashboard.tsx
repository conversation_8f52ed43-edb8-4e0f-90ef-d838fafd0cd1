/**
 * Supplier Dashboard Component
 * 
 * Comprehensive dashboard for Supplier & Manufacturer accounts
 * with real data integration and business insights
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DashboardMetricCard,
  RevenueMetricCard,
  CountMetricCard,
  PercentageMetricCard,
} from './DashboardMetricCard';
import {
  TopCustomersTable,
  TopProductsTable,
  RecentOrdersTable,
  InventoryAlertsTable,
} from './DashboardTable';
import {
  Package,
  Truck,
  Users,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  ArrowRight,
  MapPin,
  ShoppingCart,
  Warehouse,
  CreditCard,
} from 'lucide-react';
import { useSupplierDashboard } from '@/hooks/useDashboardData';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

export function SupplierDashboard() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { data, isLoading, isError, error, refresh } = useSupplierDashboard();

  if (isError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error Loading Dashboard
          </h3>
          <p className="text-gray-600 mb-4">
            {error?.message || 'Failed to load dashboard data'}
          </p>
          <Button onClick={refresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Supplier Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Overview of your business performance and key metrics
          </p>
        </div>
        <Button
          onClick={refresh}
          disabled={isLoading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <RevenueMetricCard
          title="Total Revenue"
          value={data?.totalRevenue || 0}
          subtitle="All time earnings"
          icon={DollarSign}
          trend={{
            value: data?.revenueGrowth || 0,
            label: 'vs last month',
            direction: (data?.revenueGrowth || 0) >= 0 ? 'up' : 'down',
          }}
          loading={isLoading}
        />
        
        <CountMetricCard
          title="Total Orders"
          value={data?.totalOrders || 0}
          subtitle="All time orders"
          icon={ShoppingCart}
          variant="orders"
          loading={isLoading}
        />
        
        <CountMetricCard
          title="Active Products"
          value={data?.activeProducts || 0}
          subtitle={`${data?.totalProducts || 0} total products`}
          icon={Package}
          variant="products"
          loading={isLoading}
        />
        
        <CountMetricCard
          title="Total Customers"
          value={data?.customerInsights?.totalCustomers || 0}
          subtitle={`${data?.customerInsights?.newCustomersThisMonth || 0} new this month`}
          icon={Users}
          variant="customers"
          loading={isLoading}
        />
      </div>

      {/* Revenue Breakdown */}
      <div className="grid gap-4 md:grid-cols-3">
        <RevenueMetricCard
          title="Monthly Revenue"
          value={data?.monthlyRevenue || 0}
          subtitle="Current month"
          loading={isLoading}
        />
        
        <RevenueMetricCard
          title="Weekly Revenue"
          value={data?.weeklyRevenue || 0}
          subtitle="This week"
          loading={isLoading}
        />
        
        <RevenueMetricCard
          title="Daily Revenue"
          value={data?.dailyRevenue || 0}
          subtitle="Today"
          loading={isLoading}
        />
      </div>

      {/* Orders Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <DashboardMetricCard
          title="Pending Orders"
          value={data?.pendingOrders || 0}
          subtitle="Awaiting confirmation"
          icon={Clock}
          variant="orders"
          loading={isLoading}
          onClick={() => navigate('/app/orders?status=pending')}
        />
        
        <DashboardMetricCard
          title="Confirmed Orders"
          value={data?.confirmedOrders || 0}
          subtitle="Ready for shipping"
          icon={CheckCircle}
          variant="orders"
          loading={isLoading}
          onClick={() => navigate('/app/orders?status=confirmed')}
        />
        
        <DashboardMetricCard
          title="Shipped Orders"
          value={data?.shippedOrders || 0}
          subtitle="In transit"
          icon={Truck}
          loading={isLoading}
          onClick={() => navigate('/app/orders?status=shipped')}
        />
        
        <DashboardMetricCard
          title="Delivered Orders"
          value={data?.deliveredOrders || 0}
          subtitle="Successfully delivered"
          icon={CheckCircle}
          loading={isLoading}
          onClick={() => navigate('/app/orders?status=delivered')}
        />
      </div>

      {/* Inventory Alerts */}
      <div className="grid gap-4 md:grid-cols-3">
        <DashboardMetricCard
          title="Out of Stock"
          value={data?.outOfStockProducts || 0}
          subtitle="Products need restocking"
          icon={AlertTriangle}
          variant="products"
          loading={isLoading}
          onClick={() => navigate('/app/products?status=out_of_stock')}
        />
        
        <DashboardMetricCard
          title="Low Stock"
          value={data?.lowStockProducts || 0}
          subtitle="Products running low"
          icon={Warehouse}
          variant="products"
          loading={isLoading}
          onClick={() => navigate('/app/products?status=low_stock')}
        />
        
        <DashboardMetricCard
          title="Orders Awaiting Shipment"
          value={data?.ordersAwaitingShipment || 0}
          subtitle="Need shipping assignment"
          icon={Truck}
          variant="orders"
          loading={isLoading}
          onClick={() => navigate('/app/shipping')}
        />
      </div>

      {/* Financial Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-[#DC2626]" />
            Financial Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
              ))}
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {new Intl.NumberFormat('en-DZ', {
                    style: 'currency',
                    currency: 'DZD',
                    minimumFractionDigits: 0,
                  }).format(data?.financialSummary?.netRevenue || 0)}
                </div>
                <div className="text-sm text-gray-600">Net Revenue</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {new Intl.NumberFormat('en-DZ', {
                    style: 'currency',
                    currency: 'DZD',
                    minimumFractionDigits: 0,
                  }).format(data?.financialSummary?.pendingPayments || 0)}
                </div>
                <div className="text-sm text-gray-600">Pending Payments</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data?.financialSummary?.paidOrders || 0}
                </div>
                <div className="text-sm text-gray-600">Paid Orders</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {new Intl.NumberFormat('en-DZ', {
                    style: 'currency',
                    currency: 'DZD',
                    minimumFractionDigits: 0,
                  }).format(data?.financialSummary?.arouzFees || 0)}
                </div>
                <div className="text-sm text-gray-600">AROUZ Fees</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Data Tables */}
      <div className="grid gap-6 lg:grid-cols-2">
        <TopCustomersTable
          customers={data?.topMerchantCustomers || []}
          loading={isLoading}
          onViewAll={() => navigate('/app/customers')}
        />
        
        <TopProductsTable
          products={data?.topSellingProducts || []}
          loading={isLoading}
          onViewAll={() => navigate('/app/products')}
        />
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <RecentOrdersTable
          orders={data?.recentOrders || []}
          loading={isLoading}
          onViewAll={() => navigate('/app/orders')}
        />
        
        <InventoryAlertsTable
          alerts={data?.inventoryAlerts || []}
          loading={isLoading}
          onViewAll={() => navigate('/app/inventory')}
        />
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" 
              onClick={() => navigate('/app/products/add')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5 text-[#DC2626]" />
              Add New Product
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Add new products to your inventory and start selling
            </p>
            <Button className="w-full bg-[#DC2626] hover:bg-[#B91C1C]">
              Add Product <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/app/orders')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5 text-[#DC2626]" />
              Manage Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              View and manage your orders, assign shipping companies
            </p>
            <Button variant="outline" className="w-full">
              View Orders <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </CardContent>
        </Card>


      </div>
    </div>
  );
}
