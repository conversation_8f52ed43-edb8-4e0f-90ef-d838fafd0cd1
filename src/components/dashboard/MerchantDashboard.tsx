/**
 * Merchant Dashboard Component
 * 
 * Comprehensive dashboard for Merchant Retailer accounts
 * with real data integration and business insights
 */

import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DashboardMetricCard,
  RevenueMetricCard,
  CountMetricCard,
  PercentageMetricCard,
} from './DashboardMetricCard';
import {
  TopProductsTable,
  RecentOrdersTable,
  InventoryAlertsTable,
} from './DashboardTable';
import {
  Package,
  Truck,
  Users,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  ArrowRight,
  BarChart3,
  MapPin,
  ShoppingCart,
  Warehouse,
  CreditCard,
  Eye,
  UserPlus,
  Repeat,
  Target,
} from 'lucide-react';
import { useMerchantDashboard } from '@/hooks/useDashboardData';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

export function MerchantDashboard() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { data, isLoading, isError, error, refresh } = useMerchantDashboard();

  if (isError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error Loading Dashboard
          </h3>
          <p className="text-gray-600 mb-4">
            {error?.message || 'Failed to load dashboard data'}
          </p>
          <Button onClick={refresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Merchant Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Overview of your store performance and customer insights
          </p>
        </div>
        <Button
          onClick={refresh}
          disabled={isLoading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <RevenueMetricCard
          title="Total Revenue"
          value={data?.totalRevenue || 0}
          subtitle="All time earnings"
          icon={DollarSign}
          trend={{
            value: data?.revenueGrowth || 0,
            label: 'vs last month',
            direction: (data?.revenueGrowth || 0) >= 0 ? 'up' : 'down',
          }}
          loading={isLoading}
        />
        
        <CountMetricCard
          title="Total Orders"
          value={data?.totalOrders || 0}
          subtitle="All time orders"
          icon={ShoppingCart}
          variant="orders"
          loading={isLoading}
        />
        
        <CountMetricCard
          title="Active Products"
          value={data?.activeProducts || 0}
          subtitle={`${data?.totalProducts || 0} total products`}
          icon={Package}
          variant="products"
          loading={isLoading}
        />
        
        <CountMetricCard
          title="Total Customers"
          value={data?.customerAnalytics?.totalCustomers || 0}
          subtitle={`${data?.customerAnalytics?.newCustomers || 0} new this month`}
          icon={Users}
          variant="customers"
          loading={isLoading}
        />
      </div>

      {/* Revenue Breakdown */}
      <div className="grid gap-4 md:grid-cols-4">
        <RevenueMetricCard
          title="Monthly Revenue"
          value={data?.monthlyRevenue || 0}
          subtitle="Current month"
          loading={isLoading}
        />
        
        <RevenueMetricCard
          title="Weekly Revenue"
          value={data?.weeklyRevenue || 0}
          subtitle="This week"
          loading={isLoading}
        />
        
        <RevenueMetricCard
          title="Daily Revenue"
          value={data?.dailyRevenue || 0}
          subtitle="Today"
          loading={isLoading}
        />
        
        <RevenueMetricCard
          title="Average Order Value"
          value={data?.averageOrderValue || 0}
          subtitle="Per order"
          loading={isLoading}
        />
      </div>

      {/* Customer Analytics */}
      <div className="grid gap-4 md:grid-cols-4">
        <DashboardMetricCard
          title="New Customers"
          value={data?.customerAnalytics?.newCustomers || 0}
          subtitle="This month"
          icon={UserPlus}
          variant="customers"
          loading={isLoading}
        />
        
        <DashboardMetricCard
          title="Returning Customers"
          value={data?.customerAnalytics?.returningCustomers || 0}
          subtitle="Repeat buyers"
          icon={Repeat}
          variant="customers"
          loading={isLoading}
        />
        
        <PercentageMetricCard
          title="Repeat Customer Rate"
          value={data?.customerAnalytics?.repeatCustomerRate || 0}
          subtitle="Customer retention"
          icon={Target}
          loading={isLoading}
        />
        
        <PercentageMetricCard
          title="Conversion Rate"
          value={data?.conversionRate || 0}
          subtitle="Visitors to buyers"
          icon={TrendingUp}
          loading={isLoading}
        />
      </div>

      {/* Store Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-[#DC2626]" />
            Store Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="grid gap-4 md:grid-cols-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
              ))}
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {data?.storePerformance?.productViews?.toLocaleString() || 0}
                </div>
                <div className="text-sm text-gray-600">Product Views</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {data?.storePerformance?.conversionRate?.toFixed(1) || 0}%
                </div>
                <div className="text-sm text-gray-600">Conversion Rate</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {data?.customerAnalytics?.customerLifetimeValue?.toLocaleString() || 0} DZD
                </div>
                <div className="text-sm text-gray-600">Customer Lifetime Value</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Inventory Status */}
      <div className="grid gap-4 md:grid-cols-4">
        <DashboardMetricCard
          title="In Stock"
          value={data?.inventoryStatus?.inStock || 0}
          subtitle="Products available"
          icon={CheckCircle}
          variant="products"
          loading={isLoading}
          onClick={() => navigate('/app/products?status=active')}
        />
        
        <DashboardMetricCard
          title="Low Stock"
          value={data?.inventoryStatus?.lowStock || 0}
          subtitle="Need restocking"
          icon={AlertTriangle}
          variant="products"
          loading={isLoading}
          onClick={() => navigate('/app/products?status=low_stock')}
        />
        
        <DashboardMetricCard
          title="Out of Stock"
          value={data?.inventoryStatus?.outOfStock || 0}
          subtitle="Unavailable products"
          icon={Warehouse}
          variant="products"
          loading={isLoading}
          onClick={() => navigate('/app/products?status=out_of_stock')}
        />
        
        <RevenueMetricCard
          title="Inventory Value"
          value={data?.inventoryStatus?.totalValue || 0}
          subtitle="Total stock value"
          icon={DollarSign}
          loading={isLoading}
        />
      </div>

      {/* Popular Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5 text-[#DC2626]" />
            Popular Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-12 bg-gray-200 rounded animate-pulse" />
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {data?.storePerformance?.popularCategories?.slice(0, 5).map((category, index) => (
                <div key={category.category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline" className="text-xs">
                      #{index + 1}
                    </Badge>
                    <span className="font-medium">{category.category}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{category.views} views</div>
                    <div className="text-xs text-gray-600">{category.sales} sales</div>
                  </div>
                </div>
              )) || (
                <div className="text-center text-gray-500 py-8">
                  No category data available
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Data Tables */}
      <div className="grid gap-6 lg:grid-cols-2">
        <TopProductsTable
          products={data?.topSellingProducts || []}
          loading={isLoading}
          onViewAll={() => navigate('/app/products')}
        />
        
        <RecentOrdersTable
          orders={data?.recentOrders || []}
          loading={isLoading}
          onViewAll={() => navigate('/app/orders')}
        />
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" 
              onClick={() => navigate('/app/products/add')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5 text-[#DC2626]" />
              Add New Product
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Add new products to your store and expand your catalog
            </p>
            <Button className="w-full bg-[#DC2626] hover:bg-[#B91C1C]">
              Add Product <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/app/orders')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5 text-[#DC2626]" />
              Manage Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              View and manage customer orders and delivery status
            </p>
            <Button variant="outline" className="w-full">
              View Orders <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </CardContent>
        </Card>


      </div>
    </div>
  );
}
