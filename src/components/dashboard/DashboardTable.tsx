/**
 * Dashboard Table Components
 * 
 * Reusable table components for dashboard data display
 * with sorting, filtering, and responsive design
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  MoreHorizontal, 
  ArrowUpDown, 
  Eye, 
  Phone, 
  MapPin,
  Package,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { TopCustomer, TopProduct, RecentOrder, InventoryAlert } from '@/services/dashboardService';

interface DashboardTableProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  className?: string;
  loading?: boolean;
  onViewAll?: () => void;
}

export function DashboardTable({
  title,
  subtitle,
  children,
  className,
  loading = false,
  onViewAll,
}: DashboardTableProps) {
  if (loading) {
    return (
      <Card className={cn('', className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <div className="h-5 w-32 bg-gray-200 rounded animate-pulse mb-2" />
              {subtitle && <div className="h-3 w-48 bg-gray-200 rounded animate-pulse" />}
            </div>
            <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-100 rounded animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900">
              {title}
            </CardTitle>
            {subtitle && (
              <p className="text-sm text-gray-600 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {onViewAll && (
            <Button variant="outline" size="sm" onClick={onViewAll}>
              View All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {children}
      </CardContent>
    </Card>
  );
}

/**
 * Top Customers Table
 */
interface TopCustomersTableProps {
  customers: TopCustomer[];
  loading?: boolean;
  onViewAll?: () => void;
}

export function TopCustomersTable({ customers, loading, onViewAll }: TopCustomersTableProps) {
  return (
    <DashboardTable
      title="Top Customers"
      subtitle="Your most valuable merchant partners"
      loading={loading}
      onViewAll={onViewAll}
    >
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Customer</TableHead>
            <TableHead>Orders</TableHead>
            <TableHead>Total Spent</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Last Order</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {customers.map((customer) => (
            <TableRow key={customer.id} className="hover:bg-gray-50">
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-[#FEE2E2] text-[#DC2626] text-xs">
                      {customer.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium text-sm">{customer.name}</div>
                    <div className="text-xs text-gray-500 flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      {customer.phone}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline" className="text-xs">
                  {customer.totalOrders} orders
                </Badge>
              </TableCell>
              <TableCell className="font-medium">
                {new Intl.NumberFormat('en-DZ', {
                  style: 'currency',
                  currency: 'DZD',
                  minimumFractionDigits: 0,
                }).format(customer.totalSpent)}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <MapPin className="h-3 w-3" />
                  {customer.wilaya}
                </div>
              </TableCell>
              <TableCell className="text-sm text-gray-600">
                {new Date(customer.lastOrderDate).toLocaleDateString()}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </DashboardTable>
  );
}

/**
 * Top Products Table
 */
interface TopProductsTableProps {
  products: TopProduct[];
  loading?: boolean;
  onViewAll?: () => void;
}

export function TopProductsTable({ products, loading, onViewAll }: TopProductsTableProps) {
  return (
    <DashboardTable
      title="Top Selling Products"
      subtitle="Your best performing products"
      loading={loading}
      onViewAll={onViewAll}
    >
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Product</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Sold</TableHead>
            <TableHead>Revenue</TableHead>
            <TableHead>Stock</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => (
            <TableRow key={product.id} className="hover:bg-gray-50">
              <TableCell>
                <div className="flex items-center gap-3">
                  {product.image ? (
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="h-8 w-8 rounded object-cover"
                    />
                  ) : (
                    <div className="h-8 w-8 rounded bg-gray-100 flex items-center justify-center">
                      <Package className="h-4 w-4 text-gray-400" />
                    </div>
                  )}
                  <div>
                    <div className="font-medium text-sm">{product.name}</div>
                    <div className="text-xs text-gray-500">ID: {product.id}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline" className="text-xs">
                  {product.category}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <span className="font-medium">{product.totalSold}</span>
                  {product.growth > 0 && (
                    <TrendingUp className="h-3 w-3 text-green-600" />
                  )}
                </div>
              </TableCell>
              <TableCell className="font-medium">
                {new Intl.NumberFormat('en-DZ', {
                  style: 'currency',
                  currency: 'DZD',
                  minimumFractionDigits: 0,
                }).format(product.revenue)}
              </TableCell>
              <TableCell>
                <Badge 
                  variant={product.stockQuantity > 10 ? "default" : "destructive"}
                  className="text-xs"
                >
                  {product.stockQuantity} units
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </DashboardTable>
  );
}

/**
 * Recent Orders Table
 */
interface RecentOrdersTableProps {
  orders: RecentOrder[];
  loading?: boolean;
  onViewAll?: () => void;
}

export function RecentOrdersTable({ orders, loading, onViewAll }: RecentOrdersTableProps) {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: 'Pending' },
      confirmed: { variant: 'default' as const, label: 'Confirmed' },
      shipped: { variant: 'outline' as const, label: 'Shipped' },
      delivered: { variant: 'default' as const, label: 'Delivered' },
      cancelled: { variant: 'destructive' as const, label: 'Cancelled' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Badge variant={config.variant} className="text-xs">
        {config.label}
      </Badge>
    );
  };

  return (
    <DashboardTable
      title="Recent Orders"
      subtitle="Latest orders from your customers"
      loading={loading}
      onViewAll={onViewAll}
    >
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.map((order) => (
            <TableRow key={order.id} className="hover:bg-gray-50">
              <TableCell>
                <div>
                  <div className="font-medium text-sm">#{order.orderNumber}</div>
                  <div className="text-xs text-gray-500">{order.itemCount} items</div>
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium text-sm">{order.customerName}</div>
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {order.deliveryWilaya}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {getStatusBadge(order.status)}
              </TableCell>
              <TableCell className="font-medium">
                {new Intl.NumberFormat('en-DZ', {
                  style: 'currency',
                  currency: 'DZD',
                  minimumFractionDigits: 0,
                }).format(order.totalAmount)}
              </TableCell>
              <TableCell className="text-sm text-gray-600">
                {new Date(order.createdAt).toLocaleDateString()}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </DashboardTable>
  );
}

/**
 * Inventory Alerts Table
 */
interface InventoryAlertsTableProps {
  alerts: InventoryAlert[];
  loading?: boolean;
  onViewAll?: () => void;
}

export function InventoryAlertsTable({ alerts, loading, onViewAll }: InventoryAlertsTableProps) {
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'out_of_stock':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'low_stock':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'reorder_needed':
        return <Package className="h-4 w-4 text-red-600" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
  };

  const getAlertBadge = (type: string) => {
    const config = {
      out_of_stock: { variant: 'destructive' as const, label: 'Out of Stock' },
      low_stock: { variant: 'secondary' as const, label: 'Low Stock' },
      reorder_needed: { variant: 'outline' as const, label: 'Reorder Needed' },
    };

    const alertConfig = config[type as keyof typeof config] || config.low_stock;
    
    return (
      <Badge variant={alertConfig.variant} className="text-xs">
        {alertConfig.label}
      </Badge>
    );
  };

  return (
    <DashboardTable
      title="Inventory Alerts"
      subtitle="Products requiring attention"
      loading={loading}
      onViewAll={onViewAll}
    >
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Product</TableHead>
            <TableHead>Alert Type</TableHead>
            <TableHead>Current Stock</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {alerts.map((alert) => (
            <TableRow key={alert.id} className="hover:bg-gray-50">
              <TableCell>
                <div className="flex items-center gap-3">
                  {getAlertIcon(alert.alertType)}
                  <div>
                    <div className="font-medium text-sm">{alert.productName}</div>
                    <div className="text-xs text-gray-500">ID: {alert.productId}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {getAlertBadge(alert.alertType)}
              </TableCell>
              <TableCell>
                <span className={cn(
                  'font-medium',
                  alert.currentStock === 0 ? 'text-red-600' : 
                  alert.currentStock <= 5 ? 'text-yellow-600' : 'text-gray-900'
                )}>
                  {alert.currentStock} units
                </span>
              </TableCell>
              <TableCell>
                <Badge variant="outline" className="text-xs">
                  {alert.category}
                </Badge>
              </TableCell>
              <TableCell>
                <Button variant="outline" size="sm">
                  Restock
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </DashboardTable>
  );
}
