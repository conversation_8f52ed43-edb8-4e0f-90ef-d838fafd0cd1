import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapP<PERSON>, <PERSON><PERSON><PERSON>riangle, CheckCircle, XCircle } from 'lucide-react';
import { findAccurateWilayaByCoordinates, testLocationAccuracy } from '@/data/algeria-accurate-location';

/**
 * CRITICAL NORTHWEST BORDER ACCURACY TEST
 * 
 * This component specifically tests the coordinate 34.834096, -1.670609
 * that was incorrectly being detected as Tlemcen when it should be
 * in a different wilaya (likely Sidi Bel Abbès or Naâma).
 */

interface TestResult {
  coordinates: { lat: number; lng: number };
  detected: any;
  expected?: string;
  testName: string;
  status: 'pass' | 'fail' | 'unknown';
  analysis: string;
}

export default function NorthwestBorderTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [systemTest, setSystemTest] = useState<any>(null);

  // Critical test cases for northwest border region
  const criticalTestCases = [
    {
      name: "CRITICAL: User reported issue",
      lat: 34.834096,
      lng: -1.670609,
      expected: "NOT Tlemcen",
      description: "User clicked near Tlemcen but got wrong detection"
    },
    {
      name: "Tlemcen center verification",
      lat: 34.8786,
      lng: -1.3150,
      expected: "Tlemcen",
      description: "Should definitely be Tlemcen"
    },
    {
      name: "Sidi Bel Abbès center",
      lat: 35.1977,
      lng: -0.6388,
      expected: "Sidi Bel Abbès",
      description: "Should definitely be Sidi Bel Abbès"
    },
    {
      name: "Naâma center",
      lat: 33.2667,
      lng: -0.3167,
      expected: "Naâma",
      description: "Should definitely be Naâma"
    },
    {
      name: "Border test: West of Tlemcen",
      lat: 34.8,
      lng: -2.0,
      expected: "NOT Tlemcen",
      description: "Far west should not be Tlemcen"
    }
  ];

  const runCriticalTests = async () => {
    setIsLoading(true);
    const results: TestResult[] = [];

    for (const testCase of criticalTestCases) {
      const detected = findAccurateWilayaByCoordinates(testCase.lat, testCase.lng);
      
      let status: 'pass' | 'fail' | 'unknown' = 'unknown';
      let analysis = '';

      if (testCase.expected === "NOT Tlemcen") {
        status = detected?.name !== "Tlemcen" ? 'pass' : 'fail';
        analysis = detected?.name === "Tlemcen" 
          ? `❌ CRITICAL BUG: Still detecting as Tlemcen!`
          : `✅ FIXED: Now correctly detects as ${detected?.name}`;
      } else {
        status = detected?.name === testCase.expected ? 'pass' : 'fail';
        analysis = detected?.name === testCase.expected
          ? `✅ Correct detection`
          : `❌ Expected ${testCase.expected}, got ${detected?.name}`;
      }

      results.push({
        coordinates: { lat: testCase.lat, lng: testCase.lng },
        detected,
        expected: testCase.expected,
        testName: testCase.name,
        status,
        analysis
      });
    }

    setTestResults(results);
    setIsLoading(false);
  };

  const runSystemTest = () => {
    const result = testLocationAccuracy();
    setSystemTest(result);
  };

  useEffect(() => {
    // Auto-run critical tests on component mount
    runCriticalTests();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fail': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800';
      case 'fail': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-orange-500" />
            Northwest Border Accuracy Test
          </CardTitle>
          <p className="text-sm text-gray-600">
            Critical test for coordinate 34.834096, -1.670609 that was incorrectly detected as Tlemcen
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={runCriticalTests} disabled={isLoading}>
              {isLoading ? 'Testing...' : 'Run Critical Tests'}
            </Button>
            <Button variant="outline" onClick={runSystemTest}>
              Run System Test
            </Button>
          </div>

          {testResults.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-semibold">Test Results:</h3>
              {testResults.map((result, index) => (
                <Card key={index} className="border-l-4 border-l-red-500">
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(result.status)}
                          <span className="font-medium">{result.testName}</span>
                          <Badge className={getStatusColor(result.status)}>
                            {result.status.toUpperCase()}
                          </Badge>
                        </div>
                        
                        <div className="text-sm space-y-1">
                          <p><strong>Coordinates:</strong> {result.coordinates.lat}, {result.coordinates.lng}</p>
                          <p><strong>Expected:</strong> {result.expected}</p>
                          <p><strong>Detected:</strong> {result.detected?.name || 'NULL'} ({result.detected?.name_ar || 'N/A'})</p>
                          <p><strong>Confidence:</strong> {((result.detected?.confidence || 0) * 100).toFixed(1)}%</p>
                        </div>
                        
                        <div className="text-sm font-medium">
                          {result.analysis}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {systemTest && (
            <Card className="border-l-4 border-l-blue-500">
              <CardContent className="pt-4">
                <h3 className="font-semibold mb-2">System Test Results:</h3>
                <div className="text-sm space-y-1">
                  <p><strong>Total Tests:</strong> {systemTest.totalTests}</p>
                  <p><strong>Passed:</strong> {systemTest.passedTests}</p>
                  <p><strong>Accuracy:</strong> {systemTest.accuracy?.toFixed(1)}%</p>
                  <p><strong>Critical Issue:</strong> {systemTest.criticalIssue}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
