import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Wrapper, Status } from '@googlemaps/react-wrapper';
import { MapP<PERSON>, <PERSON>hai<PERSON>, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLocation } from '@/contexts/LocationContext';
import { cn } from '@/lib/utils';

// Google Maps API Key - You'll need to add this to your environment variables
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

interface GoogleMapLocationSelectorProps {
  onLocationSelect?: (lat: number, lng: number) => void;
  initialCenter?: { lat: number; lng: number };
  height?: string;
  className?: string;
  showCurrentLocationButton?: boolean;
}

interface MapProps {
  center: google.maps.LatLngLiteral;
  zoom: number;
  onLocationSelect?: (lat: number, lng: number) => void;
  selectedLocation?: { lat: number; lng: number } | null;
}

// Custom Map Component
function Map({ center, zoom, onLocationSelect, selectedLocation }: MapProps) {
  const ref = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map>();
  const [marker, setMarker] = useState<google.maps.Marker>();
  const { setLocationFromCoordinates } = useLocation();

  // Initialize map
  useEffect(() => {
    if (ref.current && !map) {
      const newMap = new google.maps.Map(ref.current, {
        center,
        zoom,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
        zoomControl: true,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'on' }]
          }
        ],
        // Enable Arabic language support
        language: 'ar',
        region: 'DZ' // Algeria
      });

      setMap(newMap);

      // Add click listener
      newMap.addListener('click', (e: google.maps.MapMouseEvent) => {
        if (e.latLng) {
          const lat = e.latLng.lat();
          const lng = e.latLng.lng();
          setLocationFromCoordinates(lat, lng);
          onLocationSelect?.(lat, lng);
        }
      });
    }
  }, [ref, map, center, zoom, onLocationSelect, setLocationFromCoordinates]);

  // Update map center when it changes
  useEffect(() => {
    if (map) {
      map.setCenter(center);
    }
  }, [map, center]);

  // Update marker when selected location changes
  useEffect(() => {
    if (map && selectedLocation) {
      if (marker) {
        marker.setPosition(selectedLocation);
      } else {
        const newMarker = new google.maps.Marker({
          position: selectedLocation,
          map,
          draggable: true,
          icon: {
            path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',
            fillColor: '#DC2626',
            fillOpacity: 1,
            strokeColor: '#ffffff',
            strokeWeight: 2,
            scale: 1.5,
            anchor: new google.maps.Point(12, 24), // Anchor at the bottom point of the pin
          }
        });

        // Add drag listener
        newMarker.addListener('dragend', () => {
          const position = newMarker.getPosition();
          if (position) {
            const lat = position.lat();
            const lng = position.lng();
            setLocationFromCoordinates(lat, lng);
            onLocationSelect?.(lat, lng);
          }
        });

        setMarker(newMarker);
      }
    } else if (marker) {
      marker.setMap(null);
      setMarker(undefined);
    }
  }, [map, selectedLocation, marker, onLocationSelect, setLocationFromCoordinates]);

  return <div ref={ref} style={{ width: '100%', height: '100%' }} />;
}

// Loading component
function MapLoadingComponent() {
  return (
    <div className="flex items-center justify-center h-full bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-[#DC2626] mx-auto mb-2" />
        <p className="text-sm text-gray-600">Loading map...</p>
      </div>
    </div>
  );
}

// Error component
function MapErrorComponent() {
  return (
    <div className="flex items-center justify-center h-full bg-red-50">
      <div className="text-center p-4">
        <MapPin className="h-8 w-8 text-red-500 mx-auto mb-2" />
        <p className="text-sm text-red-600 mb-2">Failed to load Google Maps</p>
        <p className="text-xs text-red-500">Please check your internet connection</p>
      </div>
    </div>
  );
}

// Render function for the Wrapper
const render = (status: Status) => {
  switch (status) {
    case Status.LOADING:
      return <MapLoadingComponent />;
    case Status.FAILURE:
      return <MapErrorComponent />;
    case Status.SUCCESS:
      return <div>Map loaded successfully</div>;
  }
};

export function GoogleMapLocationSelector({
  onLocationSelect,
  initialCenter = { lat: 36.7538, lng: 3.0588 }, // Algiers as default
  height = '300px',
  className,
  showCurrentLocationButton = true
}: GoogleMapLocationSelectorProps) {
  const { 
    selectedLocation, 
    isLocationLoading, 
    locationError,
    requestCurrentLocation,
    setLocationError 
  } = useLocation();
  
  const [mapCenter, setMapCenter] = useState(initialCenter);

  // Update map center when selected location changes
  useEffect(() => {
    if (selectedLocation?.coordinates) {
      console.log('🗺️ GoogleMapLocationSelector: Updating map center to:', selectedLocation.coordinates);
      setMapCenter(selectedLocation.coordinates);
    }
  }, [selectedLocation]);

  const handleCurrentLocationClick = async () => {
    try {
      await requestCurrentLocation();
    } catch (error) {
      console.error('Error requesting current location:', error);
    }
  };

  // Check if Google Maps API key is available
  if (!GOOGLE_MAPS_API_KEY) {
    return (
      <div className={cn("relative", className)}>
        <div 
          className="relative rounded-lg overflow-hidden border border-gray-200 bg-yellow-50 flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-center p-4">
            <MapPin className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <p className="text-sm text-yellow-800 mb-1">Google Maps API Key Required</p>
            <p className="text-xs text-yellow-600">Add VITE_GOOGLE_MAPS_API_KEY to your .env file</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("relative", className)}>
      {/* Current Location Button */}
      {showCurrentLocationButton && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleCurrentLocationClick}
          disabled={isLocationLoading}
          className="absolute top-2 right-2 z-10 bg-white/90 backdrop-blur-sm hover:bg-white shadow-md"
        >
          {isLocationLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Crosshair className="h-4 w-4" />
          )}
          <span className="ml-1 text-xs">Current</span>
        </Button>
      )}

      {/* Map Container */}
      <div 
        className="relative rounded-lg overflow-hidden border border-gray-200"
        style={{ height }}
      >
        <Wrapper
          apiKey={GOOGLE_MAPS_API_KEY}
          render={render}
          libraries={['places']}
        >
          <Map
            center={mapCenter}
            zoom={13}
            onLocationSelect={onLocationSelect}
            selectedLocation={selectedLocation?.coordinates || null}
          />
        </Wrapper>
      </div>

      {/* Location Error Display */}
      {locationError && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
          <p className="text-xs text-red-600">{locationError}</p>
        </div>
      )}


    </div>
  );
}
