import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  findAccuratePreciseWilayaByCoordinates, 
  runDeliveryAccuracyTests,
  initializePreciseLocationSystem 
} from '@/data/algeria-precise-location';
import { MapPin, TestTube, CheckCircle, XCircle, Truck, AlertTriangle } from 'lucide-react';

/**
 * Precise Location Test Component for 100% Delivery Accuracy
 * 
 * This component tests the polygon-based location detection system
 * to ensure 100% accuracy for delivery operations.
 */
export default function PreciseLocationTest() {
  const [testLat, setTestLat] = useState('33.486435');
  const [testLng, setTestLng] = useState('0.829468');
  const [result, setResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [systemReady, setSystemReady] = useState(false);
  const [deliveryTests, setDeliveryTests] = useState<any>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);

  // Initialize system on mount
  useEffect(() => {
    const initSystem = async () => {
      console.log('🔄 Initializing precise location system...');
      const ready = await initializePreciseLocationSystem();
      setSystemReady(ready);
      
      if (ready) {
        console.log('✅ System ready for precise testing');
      } else {
        console.log('❌ System initialization failed');
      }
    };
    
    initSystem();
  }, []);

  // Critical test cases for delivery accuracy
  const criticalTestCases = [
    {
      name: "Critical Bug Fix",
      description: "Coordinates that were incorrectly showing Naâma instead of Laghouat",
      lat: 33.486435,
      lng: 0.829468,
      expectedWilaya: "Laghouat",
      priority: "CRITICAL"
    },
    {
      name: "Algiers Center",
      description: "Capital city center - must be 100% accurate",
      lat: 36.7631,
      lng: 3.0506,
      expectedWilaya: "Alger",
      priority: "HIGH"
    },
    {
      name: "Oran Port",
      description: "Major port city - critical for shipping",
      lat: 35.6969,
      lng: -0.6331,
      expectedWilaya: "Oran",
      priority: "HIGH"
    },
    {
      name: "Constantine",
      description: "Eastern Algeria hub",
      lat: 36.3650,
      lng: 6.6147,
      expectedWilaya: "Constantine",
      priority: "HIGH"
    },
    {
      name: "Border Test: Laghouat-Naâma",
      description: "Critical border area where errors occurred",
      lat: 33.2,
      lng: 0.5,
      expectedWilaya: "Laghouat",
      priority: "CRITICAL"
    }
  ];

  const handleSingleTest = async () => {
    if (!systemReady) {
      setResult({ error: 'System not ready. Please wait for initialization.' });
      return;
    }

    setIsLoading(true);
    try {
      const lat = parseFloat(testLat);
      const lng = parseFloat(testLng);
      
      if (isNaN(lat) || isNaN(lng)) {
        setResult({ error: 'Invalid coordinates' });
        return;
      }

      console.log(`🧪 Testing coordinates: ${lat}, ${lng}`);
      const detectedWilaya = await findAccuratePreciseWilayaByCoordinates(lat, lng);
      
      setResult({
        coordinates: { lat, lng },
        detected: detectedWilaya,
        timestamp: new Date().toLocaleTimeString()
      });
    } catch (error) {
      console.error('Test error:', error);
      setResult({ error: 'Error detecting location' });
    } finally {
      setIsLoading(false);
    }
  };

  const runCriticalTest = async (testCase: any) => {
    setTestLat(testCase.lat.toString());
    setTestLng(testCase.lng.toString());
    
    const detectedWilaya = await findAccuratePreciseWilayaByCoordinates(testCase.lat, testCase.lng);
    setResult({
      coordinates: { lat: testCase.lat, lng: testCase.lng },
      detected: detectedWilaya,
      expected: testCase.expectedWilaya,
      testCase: testCase.name,
      priority: testCase.priority,
      timestamp: new Date().toLocaleTimeString()
    });
  };

  const runFullDeliveryTests = async () => {
    if (!systemReady) {
      alert('System not ready. Please wait for initialization.');
      return;
    }

    setIsRunningTests(true);
    try {
      console.log('🚚 Running comprehensive delivery accuracy tests...');
      const testResults = await runDeliveryAccuracyTests();
      setDeliveryTests(testResults);
    } catch (error) {
      console.error('Error running delivery tests:', error);
    } finally {
      setIsRunningTests(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5 text-blue-600" />
            100% Precise Location Detection for Delivery
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 ${systemReady ? 'text-green-600' : 'text-red-600'}`}>
              {systemReady ? <CheckCircle className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
              <span className="font-medium">
                {systemReady ? 'System Ready - Polygon Boundaries Loaded' : 'Initializing System...'}
              </span>
            </div>
            {systemReady && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                100% Delivery Accuracy Enabled
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Manual Testing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5 text-blue-600" />
            Manual Coordinate Testing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Latitude</label>
              <Input
                type="number"
                step="any"
                value={testLat}
                onChange={(e) => setTestLat(e.target.value)}
                placeholder="Enter latitude"
                disabled={!systemReady}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Longitude</label>
              <Input
                type="number"
                step="any"
                value={testLng}
                onChange={(e) => setTestLng(e.target.value)}
                placeholder="Enter longitude"
                disabled={!systemReady}
              />
            </div>
          </div>
          
          <Button 
            onClick={handleSingleTest}
            disabled={isLoading || !systemReady}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? 'Testing...' : 'Test Precise Location'}
          </Button>
        </CardContent>
      </Card>

      {/* Critical Test Cases */}
      <Card>
        <CardHeader>
          <CardTitle>Critical Delivery Test Cases</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {criticalTestCases.map((testCase, index) => (
              <div
                key={index}
                className={`p-3 border rounded-lg hover:bg-gray-50 cursor-pointer ${
                  testCase.priority === 'CRITICAL' ? 'border-red-200 bg-red-50' : 'border-gray-200'
                }`}
                onClick={() => runCriticalTest(testCase)}
              >
                <div className="flex items-center justify-between">
                  <div className="font-medium text-sm">{testCase.name}</div>
                  <Badge 
                    variant={testCase.priority === 'CRITICAL' ? 'destructive' : 'secondary'}
                    className="text-xs"
                  >
                    {testCase.priority}
                  </Badge>
                </div>
                <div className="text-xs text-gray-600 mt-1">{testCase.description}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {testCase.lat}, {testCase.lng} → Expected: {testCase.expectedWilaya}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Comprehensive Delivery Tests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5 text-green-600" />
            Comprehensive Delivery Accuracy Tests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button 
              onClick={runFullDeliveryTests}
              disabled={isRunningTests || !systemReady}
              className="bg-green-600 hover:bg-green-700"
            >
              {isRunningTests ? 'Running Tests...' : 'Run Full Delivery Test Suite'}
            </Button>
            
            {deliveryTests && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{deliveryTests.totalTests}</div>
                    <div className="text-sm text-blue-700">Total Tests</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{deliveryTests.passedTests}</div>
                    <div className="text-sm text-green-700">Passed</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{deliveryTests.accuracy.toFixed(1)}%</div>
                    <div className="text-sm text-purple-700">Accuracy</div>
                  </div>
                </div>
                
                <Progress value={deliveryTests.accuracy} className="w-full" />
                
                <div className={`p-4 rounded-lg ${
                  deliveryTests.accuracy >= 95 ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex items-center gap-2">
                    {deliveryTests.accuracy >= 95 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className={`font-medium ${
                      deliveryTests.accuracy >= 95 ? 'text-green-900' : 'text-red-900'
                    }`}>
                      Delivery System: {deliveryTests.accuracy >= 95 ? 'READY FOR PRODUCTION ✅' : 'NEEDS IMPROVEMENT ❌'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-600" />
              Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            {result.error ? (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <XCircle className="h-4 w-4" />
                  {result.error}
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {result.coordinates && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="font-medium text-blue-900">
                      📍 Coordinates: {result.coordinates.lat}, {result.coordinates.lng}
                    </div>
                  </div>
                )}
                
                {result.detected && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="space-y-2">
                      <div className="font-medium text-green-900">
                        ✅ Detected Wilaya: {result.detected.name} ({result.detected.code})
                      </div>
                      <div className="text-green-700">
                        Arabic: {result.detected.name_ar}
                      </div>
                      <div className="flex items-center gap-4">
                        <Badge 
                          variant={result.detected.accuracy === 'perfect' ? "default" : "secondary"}
                        >
                          {result.detected.accuracy?.toUpperCase()} Accuracy
                        </Badge>
                        <Badge variant="outline">
                          {result.detected.method === 'polygon' ? 'Polygon Boundary' : 'Distance Fallback'}
                        </Badge>
                        <Badge 
                          variant={result.detected.confidence > 0.9 ? "default" : "secondary"}
                        >
                          {(result.detected.confidence * 100).toFixed(1)}% confidence
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
                
                {result.expected && (
                  <div className={`p-4 border rounded-lg ${
                    result.detected?.name === result.expected 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center gap-2">
                      {result.detected?.name === result.expected ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className={
                        result.detected?.name === result.expected 
                          ? 'text-green-900' 
                          : 'text-red-900'
                      }>
                        Expected: {result.expected} | 
                        Result: {result.detected?.name === result.expected ? 'PASS ✅' : 'FAIL ❌'}
                      </span>
                      {result.priority && (
                        <Badge 
                          variant={result.priority === 'CRITICAL' ? 'destructive' : 'secondary'}
                          className="ml-2"
                        >
                          {result.priority}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
                
                <div className="text-xs text-gray-500">
                  Tested at: {result.timestamp}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
