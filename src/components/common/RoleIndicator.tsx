import React from 'react';
import { useUser } from '@/contexts/UserContext';
import { Button } from '@/components/ui/button';
import { Building, Store } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslation } from 'react-i18next';

export function RoleIndicator() {
  const { userRole, isSupplier, isMerchant } = useUser();
  const { t } = useTranslation();

  // Only show for supplier or merchant roles
  if (!isSupplier() && !isMerchant()) {
    return null;
  }

  // Supplier role indicator
  if (isSupplier()) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "flex items-center gap-2",
                "border-[#DC2626]/20 bg-[#FEE2E2]/20",
                "hover:bg-[#FEE2E2]/40 hover:text-[#DC2626]",
                "text-[#DC2626]"
              )}
              style={{ unicodeBidi: 'isolate' }}
            >
              <Building className="h-4 w-4 text-[#DC2626] flex-shrink-0" />
              <span className="hidden sm:inline-block font-medium" style={{ unicodeBidi: 'isolate' }}>
                {t('roles.manufacturer')}
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>{t('roles.manufacturerDescription')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Merchant role indicator
  if (isMerchant()) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "flex items-center gap-2",
                "border-[#071c44]/20 bg-[#071c44]/10",
                "hover:bg-[#071c44]/20 hover:text-[#071c44]",
                "text-[#071c44]"
              )}
              style={{ unicodeBidi: 'isolate' }}
            >
              <Store className="h-4 w-4 text-[#071c44] flex-shrink-0" />
              <span className="hidden sm:inline-block font-medium" style={{ unicodeBidi: 'isolate' }}>
                {t('roles.merchant')}
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>{t('roles.merchantDescription')}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Fallback (should never reach here due to the conditions above)
  return null;
}
