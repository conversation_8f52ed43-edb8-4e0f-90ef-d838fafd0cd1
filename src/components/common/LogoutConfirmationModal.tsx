import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { LogOut, AlertTriangle, Building, Store } from 'lucide-react';
import { signOutAdminOnly } from '@/services/authService';
import { useToast } from '@/components/ui/use-toast';
import { useUser, UserRole } from '@/contexts/UserContext';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';

interface LogoutConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  role?: UserRole; // Optional role to log out from
}

export function LogoutConfirmationModal({ isOpen, onClose, role }: LogoutConfirmationModalProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { supplierRole, merchantRole, setSupplierRole, setMerchantRole } = useUser();
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);

  // Check if user has multiple roles active
  const hasMultipleRoles = supplierRole && merchantRole;

  // Determine if this is a role-specific logout
  const isRoleSpecificLogout = !!role;

  // Check if this is the last active role
  const isLastActiveRole =
    (role === 'supplier' && !merchantRole) ||
    (role === 'merchant' && !supplierRole);

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);

      // CRITICAL FIX: Use admin-only logout to preserve consumer and shipping company sessions
      const { success, error } = await signOutAdminOnly(role);

      if (success) {
        // Update local role state
        if (role === 'supplier') {
          setSupplierRole(false);
        } else if (role === 'merchant') {
          setMerchantRole(false);
        } else {
          // Complete logout
          setSupplierRole(false);
          setMerchantRole(false);
        }

        // Show success toast
        toast({
          title: t('auth.logoutSuccess'),
          description: t('auth.logoutSuccessDescription'),
        });

        // Close the modal
        onClose();

        // Redirect to home page after a short delay if this is a complete logout
        // or the last active role
        if (!role || isLastActiveRole) {
          setTimeout(() => {
            navigate('/');
          }, 500);
        }
      } else {
        // Show error toast
        toast({
          title: t('auth.logoutFailed'),
          description: error?.message || t('auth.logoutFailedDescription'),
          variant: "destructive",
        });

        // Close the modal
        onClose();
      }
    } catch (error) {
      console.error('Error during logout:', error);

      // Show error toast
      toast({
        title: t('auth.logoutFailed'),
        description: t('auth.unexpectedError'),
        variant: "destructive",
      });

      // Close the modal
      onClose();
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Get the appropriate title and message based on the logout type
  const getDialogTitle = () => {
    if (isRoleSpecificLogout) {
      if (role === 'supplier') {
        return (
          <div className="flex items-center gap-2 text-xl">
            <Building className="h-5 w-5 text-[#DC2626]" />
            {t('auth.logoutFromSupplierAccount')}
          </div>
        );
      } else if (role === 'merchant') {
        return (
          <div className="flex items-center gap-2 text-xl">
            <Store className="h-5 w-5 text-[#071c44]" />
            {t('auth.logoutFromMerchantAccount')}
          </div>
        );
      }
    }

    return (
      <div className="flex items-center gap-2 text-xl">
        <LogOut className="h-5 w-5 text-primary" />
        {t('auth.confirmLogout')}
      </div>
    );
  };

  const getDialogMessage = () => {
    if (isRoleSpecificLogout) {
      if (hasMultipleRoles) {
        return t('auth.logoutRoleSpecificMessage');
      } else {
        return t('auth.logoutConfirmationMessage');
      }
    }
    return t('auth.logoutConfirmationMessage');
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>
            {getDialogTitle()}
          </AlertDialogTitle>
          <AlertDialogDescription className="pt-2">
            {getDialogMessage()}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="mt-4">
          <AlertDialogCancel disabled={isLoggingOut}>
            {t('actions.cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              handleLogout();
            }}
            disabled={isLoggingOut}
            className="bg-primary hover:bg-primary/90"
          >
            {isLoggingOut ? t('auth.loggingOut') : t('auth.confirmLogoutAction')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Standalone button component that opens the logout confirmation modal
export function LogoutButton({ role }: { role?: UserRole }) {
  const { t } = useTranslation();
  const [isConfirmationOpen, setIsConfirmationOpen] = React.useState(false);
  const { supplierRole, merchantRole } = useUser();

  // Determine if we should show role-specific logout options
  const hasMultipleRoles = supplierRole && merchantRole;

  return (
    <>
      <Button
        variant="ghost"
        className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
        onClick={() => setIsConfirmationOpen(true)}
      >
        <LogOut className="mr-2 h-4 w-4" />
        {role ?
          (role === 'supplier' ? t('auth.logoutFromSupplier') : t('auth.logoutFromMerchant')) :
          t('navigation.logout')
        }
      </Button>

      <LogoutConfirmationModal
        isOpen={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        role={role}
      />
    </>
  );
}
