import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  Truck, 
  Store,
  Clock,
  Shield,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Banknote,
  MapPin
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { CheckoutData, PaymentMethod } from '@/pages/BasketPage';

interface PaymentMethodStepProps {
  data: CheckoutData;
  onComplete: (data: Partial<CheckoutData>) => void;
  onBack: () => void;
}

const PAYMENT_METHODS = [
  {
    id: 'cash_on_delivery' as PaymentMethod,
    name: 'Cash on Delivery (Express 24h)',
    description: 'Pay when your order is delivered to your address',
    icon: Truck,
    features: [
      'Express 24-48 hour delivery',
      'Pay directly to the delivery driver',
      'Inspect products before payment',
      'Available in all 58 wilayas'
    ],
    badge: 'Most Popular',
    badgeColor: 'bg-green-100 text-green-800'
  },
  {
    id: 'store_pickup' as PaymentMethod,
    name: 'Store Pickup & Pay',
    description: 'Pick up your order from the store and pay in person',
    icon: Store,
    features: [
      'No delivery fees',
      'Flexible pickup times',
      'Direct interaction with seller',
      'Immediate product inspection'
    ],
    badge: 'Save Money',
    badgeColor: 'bg-blue-100 text-blue-800'
  }
];

export function PaymentMethodStep({ data, onComplete, onBack }: PaymentMethodStepProps) {
  const { t } = useTranslation();
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(data.paymentMethod);

  const handleMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
  };

  const handleContinue = () => {
    if (selectedMethod) {
      onComplete({ paymentMethod: selectedMethod });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CreditCard className="h-6 w-6 text-[#DC2626]" />
          <span>{t('checkout.step3.title')}</span>
        </CardTitle>
        <p className="text-gray-600">
          {t('checkout.step3.description')}
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Payment Methods */}
          <RadioGroup
            value={selectedMethod || ''}
            onValueChange={(value) => handleMethodSelect(value as PaymentMethod)}
            className="space-y-4"
          >
            {PAYMENT_METHODS.map((method) => (
              <motion.div
                key={method.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`
                  relative border-2 rounded-lg p-6 cursor-pointer transition-all
                  ${selectedMethod === method.id 
                    ? 'border-[#DC2626] bg-[#DC2626]/5' 
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}
                onClick={() => handleMethodSelect(method.id)}
              >
                <div className="flex items-start space-x-4">
                  <RadioGroupItem 
                    value={method.id} 
                    id={method.id}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <div className={`
                          w-12 h-12 rounded-lg flex items-center justify-center
                          ${selectedMethod === method.id 
                            ? 'bg-[#DC2626] text-white' 
                            : 'bg-gray-100 text-gray-600'
                          }
                        `}>
                          <method.icon className="h-6 w-6" />
                        </div>
                        <div>
                          <Label 
                            htmlFor={method.id}
                            className="text-lg font-semibold cursor-pointer"
                          >
                            {method.name}
                          </Label>
                          <p className="text-sm text-gray-600 mt-1">
                            {method.description}
                          </p>
                        </div>
                      </div>
                      <Badge className={method.badgeColor}>
                        {method.badge}
                      </Badge>
                    </div>

                    {/* Features */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                      {method.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Method-specific details */}
                    {method.id === 'cash_on_delivery' && (
                      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div className="flex items-center space-x-2 text-yellow-800">
                          <Banknote className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {t('checkout.step3.codNote')}
                          </span>
                        </div>
                        <p className="text-xs text-yellow-700 mt-1">
                          {t('checkout.step3.codDescription')}
                        </p>
                      </div>
                    )}

                    {method.id === 'store_pickup' && (
                      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <div className="flex items-center space-x-2 text-blue-800">
                          <MapPin className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {t('checkout.step3.pickupNote')}
                          </span>
                        </div>
                        <p className="text-xs text-blue-700 mt-1">
                          {t('checkout.step3.pickupDescription')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Selection indicator */}
                {selectedMethod === method.id && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute top-4 right-4 w-6 h-6 bg-[#DC2626] rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="h-4 w-4 text-white" />
                  </motion.div>
                )}
              </motion.div>
            ))}
          </RadioGroup>

          {/* Security Notice */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-gray-800 mb-2">
              <Shield className="h-5 w-5 text-green-600" />
              <span className="font-medium">{t('checkout.step3.securityTitle')}</span>
            </div>
            <div className="space-y-1 text-sm text-gray-600">
              <p>• {t('checkout.step3.security1')}</p>
              <p>• {t('checkout.step3.security2')}</p>
              <p>• {t('checkout.step3.security3')}</p>
            </div>
          </div>

          {/* Selected Method Summary */}
          {selectedMethod && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="bg-[#DC2626]/10 border border-[#DC2626]/20 rounded-lg p-4"
            >
              <div className="flex items-center space-x-2 text-[#DC2626] mb-2">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">{t('checkout.step3.selectedMethod')}</span>
              </div>
              <div className="flex items-center space-x-3">
                {selectedMethod === 'cash_on_delivery' ? (
                  <Truck className="h-5 w-5 text-[#DC2626]" />
                ) : (
                  <Store className="h-5 w-5 text-[#DC2626]" />
                )}
                <span className="font-medium text-gray-900">
                  {PAYMENT_METHODS.find(m => m.id === selectedMethod)?.name}
                </span>
              </div>
            </motion.div>
          )}

          {/* Future Payment Methods Notice */}
          <div className="text-center py-4 border-t border-gray-200">
            <div className="flex items-center justify-center space-x-2 text-gray-500 mb-2">
              <Clock className="h-4 w-4" />
              <span className="text-sm">{t('checkout.step3.comingSoon')}</span>
            </div>
            <p className="text-xs text-gray-400">
              {t('checkout.step3.futurePayments')}
            </p>
          </div>

          {/* Professional Navigation */}
          <div className="flex justify-between items-center pt-8 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={onBack}
              className="px-6 py-3 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 rounded-lg font-medium"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.back')}
            </Button>

            <Button
              onClick={handleContinue}
              disabled={!selectedMethod}
              className="px-8 py-3 bg-gradient-to-r from-[#DC2626] to-[#EF4444] hover:from-[#B91C1C] hover:to-[#e58500] text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {t('common.continue')}
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </motion.div>
      </CardContent>
    </Card>
  );
}
