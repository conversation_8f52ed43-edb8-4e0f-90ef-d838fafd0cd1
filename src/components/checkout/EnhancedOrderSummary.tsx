import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { 
  ShoppingCart, 
  Truck, 
  Store, 
  Info, 
  Calculator,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { CartItem } from '@/contexts/CartContext';
import { CheckoutData } from '@/pages/BasketPage';
import { calculateCheckoutFees, ArouzFeeBreakdown } from '@/services/arouzFeeService';

interface EnhancedOrderSummaryProps {
  cartItems: CartItem[];
  checkoutData: CheckoutData;
  totalPrice: number;
  currentStep: number;
}

export function EnhancedOrderSummary({
  cartItems,
  checkoutData,
  totalPrice,
  currentStep
}: EnhancedOrderSummaryProps) {
  const { t } = useTranslation();
  const [feeBreakdown, setFeeBreakdown] = useState<ArouzFeeBreakdown | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [calculationError, setCalculationError] = useState<string | null>(null);

  // Calculate fees when shipping info is available
  useEffect(() => {
    if (currentStep >= 4 && checkoutData.shippingInfo) {
      calculateFees();
    }
  }, [checkoutData.shippingInfo, totalPrice, cartItems, currentStep]);

  const calculateFees = async () => {
    try {
      setIsCalculating(true);
      setCalculationError(null);
      
      console.log('💰 Calculating fees for order summary...');
      
      const shippingCost = checkoutData.shippingInfo?.shipping_cost || 0;
      const breakdown = await calculateCheckoutFees(totalPrice, shippingCost, cartItems);
      
      setFeeBreakdown(breakdown);
      console.log('✅ Fee calculation completed for order summary:', breakdown);
    } catch (error) {
      console.error('❌ Error calculating fees:', error);
      setCalculationError('Failed to calculate fees');
    } finally {
      setIsCalculating(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getShippingDisplay = () => {
    if (currentStep < 4 || !checkoutData.shippingInfo) {
      return <span className="text-gray-500">{t('checkout.calculatedAtCheckout')}</span>;
    }
    return <span className="font-medium">{formatCurrency(checkoutData.shippingInfo.shipping_cost)}</span>;
  };

  const getFeesDisplay = () => {
    if (currentStep < 4 || !feeBreakdown) {
      return <span className="text-gray-500">{t('checkout.calculatedAtCheckout')}</span>;
    }
    
    if (isCalculating) {
      return <span className="text-gray-500">Calculating...</span>;
    }
    
    if (calculationError) {
      return <span className="text-red-500 text-xs">Error</span>;
    }
    
    return <span className="font-medium">{formatCurrency(feeBreakdown.total_fees)}</span>;
  };

  const getFinalTotal = () => {
    if (feeBreakdown) {
      return feeBreakdown.grand_total;
    }
    
    const shippingCost = checkoutData.shippingInfo?.shipping_cost || 0;
    return totalPrice + shippingCost;
  };

  return (
    <Card className="sticky top-32">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <ShoppingCart className="h-5 w-5" />
          <span>{t('checkout.orderSummary')}</span>
          {isCalculating && (
            <div className="w-4 h-4 border-2 border-[#DC2626] border-t-transparent rounded-full animate-spin"></div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {cartItems.map((item) => (
            <div key={item.id} className="flex items-center space-x-3">
              <img
                src={item.image || '/placeholder.svg'}
                alt={item.name}
                className="w-12 h-12 object-cover rounded-md"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {item.name}
                </p>
                <p className="text-xs text-gray-500">
                  {item.quantity} × {formatCurrency(item.price)}
                </p>
              </div>
              <p className="text-sm font-medium">
                {formatCurrency(item.price * item.quantity)}
              </p>
            </div>
          ))}
        </div>

        <Separator />

        {/* Pricing Breakdown */}
        <div className="space-y-3">
          {/* Subtotal */}
          <div className="flex justify-between text-sm">
            <span>{t('checkout.subtotal')}</span>
            <span className="font-medium">{formatCurrency(totalPrice)}</span>
          </div>

          {/* Shipping */}
          <div className="flex justify-between text-sm">
            <div className="flex items-center space-x-1">
              <span>{t('checkout.shipping')}</span>
              {checkoutData.shippingInfo && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-3 w-3 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">{checkoutData.shippingInfo.company_name}</p>
                      <p className="text-xs">{checkoutData.shippingInfo.estimated_delivery}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            {getShippingDisplay()}
          </div>

          {/* AROUZ Fees */}
          <div className="flex justify-between text-sm">
            <div className="flex items-center space-x-1">
              <span>{t('checkout.arouzFee')}</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-3 w-3 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-xs space-y-1">
                      <p>Marketplace commission and service fees</p>
                      {feeBreakdown && feeBreakdown.fees.map((fee, index) => (
                        <p key={index}>
                          {fee.fee_name}: {formatCurrency(fee.calculated_amount)}
                        </p>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            {getFeesDisplay()}
          </div>

          {/* Fee Breakdown (when available) */}
          {feeBreakdown && feeBreakdown.fees.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="bg-red-50 rounded-lg p-3 space-y-2"
            >
              <div className="flex items-center space-x-2 text-xs font-medium text-[#DC2626]">
                <Calculator className="h-3 w-3" />
                <span>Fee Breakdown</span>
              </div>
              {feeBreakdown.fees.map((fee, index) => (
                <div key={index} className="flex justify-between text-xs">
                  <span className="text-gray-600">
                    {fee.fee_name}
                    {fee.calculation_method === 'percentage' && ` (${fee.rate}%)`}
                  </span>
                  <span className="font-medium">{formatCurrency(fee.calculated_amount)}</span>
                </div>
              ))}
            </motion.div>
          )}
        </div>

        <Separator />

        {/* Total */}
        <div className="flex justify-between font-bold text-lg">
          <span>{t('checkout.total')}</span>
          <span className="text-[#DC2626]">
            {formatCurrency(getFinalTotal())}
          </span>
        </div>

        {/* Payment Method Badge */}
        {checkoutData.paymentMethod && (
          <div className="pt-2">
            <Badge variant="outline" className="w-full justify-center py-2">
              {checkoutData.paymentMethod === 'cash_on_delivery' ? (
                <><Truck className="h-4 w-4 mr-2" /> {t('checkout.cashOnDelivery')}</>
              ) : (
                <><Store className="h-4 w-4 mr-2" /> {t('checkout.storePickup')}</>
              )}
            </Badge>
          </div>
        )}

        {/* Shipping Method Badge */}
        {checkoutData.shippingInfo && (
          <div>
            <Badge variant="outline" className="w-full justify-center py-2 bg-blue-50 border-blue-200">
              <Truck className="h-4 w-4 mr-2 text-blue-600" />
              <span className="text-blue-700">{checkoutData.shippingInfo.company_name}</span>
            </Badge>
          </div>
        )}

        {/* Calculation Status */}
        {calculationError && (
          <div className="flex items-center space-x-2 text-xs text-red-600 bg-red-50 p-2 rounded">
            <AlertCircle className="h-3 w-3" />
            <span>Fee calculation error</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={calculateFees}
              className="ml-auto h-6 px-2 text-xs"
            >
              Retry
            </Button>
          </div>
        )}

        {feeBreakdown && !calculationError && (
          <div className="flex items-center space-x-2 text-xs text-green-600 bg-green-50 p-2 rounded">
            <CheckCircle className="h-3 w-3" />
            <span>All fees calculated</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
