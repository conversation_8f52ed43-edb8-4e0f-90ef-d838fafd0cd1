import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Loader2, CheckCircle, Lock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { updatePassword } from '@/services/authService';
import { useToast } from '@/components/ui/use-toast';
import { PasswordStrengthIndicator } from './PasswordStrengthIndicator';
import { checkPasswordStrength } from '@/services/authService';

interface ResetPasswordFormProps {
  onComplete: () => void;
}

export function ResetPasswordForm({ onComplete }: ResetPasswordFormProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  
  const passwordStrength = checkPasswordStrength(password);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!password || !confirmPassword || isLoading) return;
    
    if (password !== confirmPassword) {
      toast({
        title: t('auth.passwordMismatch'),
        description: t('auth.passwordMismatchDescription'),
        variant: 'destructive',
      });
      return;
    }
    
    if (!passwordStrength.isValid) {
      toast({
        title: t('auth.passwordTooWeak'),
        description: t('auth.passwordRequirements'),
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const { success, error } = await updatePassword(password);
      
      if (error) {
        toast({
          title: t('auth.resetPasswordFailed'),
          description: error.message,
          variant: 'destructive',
        });
        setIsLoading(false);
        return;
      }
      
      if (success) {
        setIsSuccess(true);
        toast({
          title: t('auth.passwordResetSuccess'),
          description: t('auth.passwordResetSuccessDescription'),
        });
        
        // Redirect after a short delay
        setTimeout(() => {
          onComplete();
        }, 2000);
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      toast({
        title: t('auth.resetPasswordFailed'),
        description: t('auth.unexpectedError'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-4">
      {!isSuccess ? (
        <>
          <div className="text-center mb-6">
            <h2 className="text-lg font-semibold">{t('auth.resetPassword')}</h2>
            <p className="text-sm text-muted-foreground mt-1">
              {t('auth.resetPasswordDescription')}
            </p>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-1.5">
              <Label htmlFor="password" className="text-sm font-medium">
                {t('auth.newPassword')}
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                  <Lock className="h-4 w-4" />
                </div>
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder={t('auth.enterNewPassword')}
                  className="w-full pl-10 pr-10 h-10"
                  required
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              
              <PasswordStrengthIndicator password={password} className="mt-2" />
            </div>
            
            <div className="space-y-1.5">
              <Label htmlFor="confirmPassword" className="text-sm font-medium">
                {t('auth.confirmPassword')}
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                  <Lock className="h-4 w-4" />
                </div>
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder={t('auth.confirmNewPassword')}
                  className="w-full pl-10 pr-10 h-10"
                  required
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              {password && confirmPassword && password !== confirmPassword && (
                <p className="text-xs text-red-500 mt-1">
                  {t('auth.passwordMismatchDescription')}
                </p>
              )}
            </div>
            
            <Button
              type="submit"
              disabled={!password || !confirmPassword || password !== confirmPassword || !passwordStrength.isValid || isLoading}
              className="w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white font-semibold py-2.5 rounded-lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('auth.processing')}
                </>
              ) : (
                t('auth.resetPassword')
              )}
            </Button>
          </form>
        </>
      ) : (
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <CheckCircle className="h-12 w-12 text-green-500" />
          </div>
          <h2 className="text-lg font-semibold">{t('auth.passwordResetSuccess')}</h2>
          <p className="text-sm text-muted-foreground">
            {t('auth.passwordResetSuccessDescription')}
          </p>
        </div>
      )}
    </div>
  );
}
