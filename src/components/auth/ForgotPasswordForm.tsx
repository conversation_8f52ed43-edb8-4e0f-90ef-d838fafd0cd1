import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail, Loader2, ArrowLeft, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { sendPasswordResetEmail } from '@/services/authService';
import { useToast } from '@/components/ui/use-toast';

interface ForgotPasswordFormProps {
  onBack: () => void;
}

export function ForgotPasswordForm({ onBack }: ForgotPasswordFormProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [rateLimitInfo, setRateLimitInfo] = useState<any>(null);
  const [countdown, setCountdown] = useState(0);

  // Handle countdown timer for rate limiting
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Handle rate limit countdown
  useEffect(() => {
    if (rateLimitInfo?.waitTime) {
      setCountdown(rateLimitInfo.waitTime);
    } else if (rateLimitInfo?.resetTime) {
      const resetTime = new Date(rateLimitInfo.resetTime).getTime();
      const now = Date.now();
      const secondsRemaining = Math.max(0, Math.ceil((resetTime - now) / 1000));
      setCountdown(secondsRemaining);
    }
  }, [rateLimitInfo]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || isLoading) return;
    
    setIsLoading(true);
    
    try {
      const { success, error, rateLimitInfo: newRateLimitInfo } = await sendPasswordResetEmail(email);

      // Update rate limit info
      if (newRateLimitInfo) {
        setRateLimitInfo(newRateLimitInfo);
      }

      if (error) {
        // Check if it's a rate limit error
        if (error.message.includes('wait') || error.message.includes('Too many')) {
          toast({
            title: 'Rate Limit Exceeded',
            description: error.message,
            variant: 'destructive',
          });
        } else {
          toast({
            title: t('auth.resetPasswordFailed'),
            description: error.message,
            variant: 'destructive',
          });
        }
        setIsLoading(false);
        return;
      }

      if (success) {
        setIsSuccess(true);
        setRateLimitInfo(null); // Clear rate limit info on success
        toast({
          title: t('auth.resetPasswordEmailSent'),
          description: t('auth.resetPasswordEmailSentDescription'),
        });
      }
    } catch (error) {
      console.error('Error sending password reset email:', error);
      toast({
        title: t('auth.resetPasswordFailed'),
        description: t('auth.unexpectedError'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="space-y-4">
      {!isSuccess ? (
        <>
          <div className="text-center mb-6">
            <h2 className="text-lg font-semibold">{t('auth.forgotPassword')}</h2>
            <p className="text-sm text-muted-foreground mt-1">
              {t('auth.forgotPasswordDescription')}
            </p>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-1.5">
              <Label htmlFor="email" className="text-sm font-medium">
                {t('auth.email')}
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                  <Mail className="h-4 w-4" />
                </div>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full pl-10 h-10"
                  required
                  autoComplete="email"
                />
              </div>
            </div>
            
            {/* Rate Limit Warning */}
            {rateLimitInfo && countdown > 0 && (
              <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm text-yellow-800">
                <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                <div className="flex-1">
                  <p className="font-medium">Rate limit active</p>
                  <p>
                    {rateLimitInfo.waitTime ?
                      `Please wait ${countdown} seconds before trying again.` :
                      `Too many attempts. Try again in ${Math.ceil(countdown / 60)} minutes.`
                    }
                  </p>
                  {rateLimitInfo.remainingAttempts !== undefined && (
                    <p className="text-xs mt-1">
                      Remaining attempts: {rateLimitInfo.remainingAttempts}
                    </p>
                  )}
                </div>
                <Clock className="h-4 w-4 flex-shrink-0" />
              </div>
            )}

            <Button
              type="submit"
              disabled={!email || isLoading || countdown > 0}
              className="w-full bg-[#DC2626] hover:bg-[#DC2626]/90 text-white font-semibold py-2.5 rounded-lg disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('auth.processing')}
                </>
              ) : countdown > 0 ? (
                <>
                  <Clock className="mr-2 h-4 w-4" />
                  Wait {countdown}s
                </>
              ) : (
                t('auth.sendResetLink')
              )}
            </Button>
          </form>
          
          <Button
            variant="ghost"
            className="w-full flex items-center justify-center gap-2 mt-4"
            onClick={onBack}
          >
            <ArrowLeft className="h-4 w-4" />
            {t('auth.backToLogin')}
          </Button>
        </>
      ) : (
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <CheckCircle className="h-12 w-12 text-green-500" />
          </div>
          <h2 className="text-lg font-semibold">{t('auth.checkYourEmail')}</h2>
          <p className="text-sm text-muted-foreground">
            {t('auth.resetPasswordEmailSentDescription')}
          </p>
          <Button
            variant="ghost"
            className="w-full flex items-center justify-center gap-2 mt-4"
            onClick={onBack}
          >
            <ArrowLeft className="h-4 w-4" />
            {t('auth.backToLogin')}
          </Button>
        </div>
      )}
    </div>
  );
}
