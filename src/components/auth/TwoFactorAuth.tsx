import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Shield, CheckCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { logAuthEvent } from '@/services/auditService';

interface TwoFactorAuthProps {
  userId: string;
  onVerified: () => void;
  onCancel: () => void;
}

export function TwoFactorAuth({ userId, onVerified, onCancel }: TwoFactorAuthProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [code, setCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes in seconds
  const [resendDisabled, setResendDisabled] = useState(true);
  const [resendCountdown, setResendCountdown] = useState(60); // 1 minute in seconds
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Focus the input field on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  
  // Countdown timer for code expiration
  useEffect(() => {
    if (timeLeft <= 0) {
      toast({
        title: t('twoFactor.codeExpired', 'Verification code expired'),
        description: t('twoFactor.requestNewCode', 'Please request a new code'),
        variant: 'destructive',
      });
      return;
    }
    
    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [timeLeft, t, toast]);
  
  // Countdown timer for resend button
  useEffect(() => {
    if (resendCountdown <= 0) {
      setResendDisabled(false);
      return;
    }
    
    const timer = setTimeout(() => {
      setResendCountdown(resendCountdown - 1);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [resendCountdown]);
  
  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Handle code verification
  const handleVerify = async () => {
    if (code.length !== 6 || isLoading) return;
    
    setIsLoading(true);
    
    try {
      // In a real implementation, this would call an API to verify the code
      // For this example, we'll simulate a successful verification with a delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For demo purposes, accept any 6-digit code
      const isValid = code.length === 6 && /^\d+$/.test(code);
      
      if (isValid) {
        // Log successful 2FA verification
        await logAuthEvent(userId, 'login', { 
          method: '2fa', 
          success: true 
        });
        
        toast({
          title: t('twoFactor.verificationSuccessful', 'Verification successful'),
          description: t('twoFactor.redirecting', 'Redirecting to your account...'),
        });
        
        // Call the onVerified callback
        setTimeout(() => {
          onVerified();
        }, 1000);
      } else {
        // Log failed 2FA attempt
        await logAuthEvent(userId, 'login_failed', { 
          method: '2fa', 
          reason: 'invalid_code' 
        });
        
        toast({
          title: t('twoFactor.verificationFailed', 'Verification failed'),
          description: t('twoFactor.invalidCode', 'Invalid verification code. Please try again.'),
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error verifying 2FA code:', error);
      
      toast({
        title: t('twoFactor.verificationError', 'Verification error'),
        description: t('twoFactor.unexpectedError', 'An unexpected error occurred. Please try again.'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle code resend
  const handleResend = async () => {
    setResendDisabled(true);
    setResendCountdown(60);
    setTimeLeft(300);
    
    try {
      // In a real implementation, this would call an API to resend the code
      // For this example, we'll simulate a successful resend with a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: t('twoFactor.codeSent', 'New code sent'),
        description: t('twoFactor.checkYourDevice', 'Please check your device for the new verification code.'),
      });
    } catch (error) {
      console.error('Error resending 2FA code:', error);
      
      toast({
        title: t('twoFactor.resendError', 'Error sending code'),
        description: t('twoFactor.unexpectedError', 'An unexpected error occurred. Please try again.'),
        variant: 'destructive',
      });
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col items-center text-center">
        <Shield className="h-12 w-12 text-primary" />
        <h2 className="mt-4 text-xl font-semibold">
          {t('twoFactor.title', 'Two-Factor Authentication')}
        </h2>
        <p className="mt-2 text-sm text-muted-foreground">
          {t('twoFactor.description', 'Enter the 6-digit verification code sent to your device')}
        </p>
      </div>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="verificationCode">
            {t('twoFactor.verificationCode', 'Verification Code')}
          </Label>
          <Input
            id="verificationCode"
            ref={inputRef}
            value={code}
            onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            placeholder="123456"
            className="text-center text-lg tracking-widest"
            inputMode="numeric"
            maxLength={6}
            autoComplete="one-time-code"
          />
        </div>
        
        <div className="text-center text-sm">
          <p className="text-muted-foreground">
            {t('twoFactor.codeExpires', 'Code expires in')} <span className="font-medium">{formatTime(timeLeft)}</span>
          </p>
        </div>
        
        <Button
          onClick={handleVerify}
          disabled={code.length !== 6 || isLoading}
          className="w-full bg-[#DC2626] hover:bg-[#B91C1C]"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('twoFactor.verifying', 'Verifying...')}
            </>
          ) : (
            t('twoFactor.verify', 'Verify')
          )}
        </Button>
        
        <div className="flex justify-between items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="text-sm"
          >
            {t('twoFactor.cancel', 'Cancel')}
          </Button>
          
          <Button
            variant="link"
            size="sm"
            onClick={handleResend}
            disabled={resendDisabled}
            className="text-sm"
          >
            {resendDisabled
              ? t('twoFactor.resendIn', 'Resend in {{seconds}}s', { seconds: resendCountdown })
              : t('twoFactor.resendCode', 'Resend code')}
          </Button>
        </div>
      </div>
      
      <div className="text-xs text-center text-muted-foreground">
        <p>
          {t('twoFactor.troubleShootingText', "Having trouble? Contact your administrator for assistance.")}
        </p>
      </div>
    </div>
  );
}
