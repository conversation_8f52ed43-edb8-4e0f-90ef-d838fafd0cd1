@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 215 30% 18%;
    --primary-foreground: 210 40% 98%;

    --secondary: 0 84% 60%; /* KALIX red in HSL */
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 350 89% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 215 30% 18%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 84% 60%; /* KALIX red in HSL */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 25% 25%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 215 30% 25%;
    --sidebar-ring: 0 84% 60%; /* KALIX red in HSL */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-soft-white text-graphite font-ibm-plex;
  }

  /* RTL specific adjustments */
  .rtl {
    direction: rtl;
    text-align: right;
  }

  /* RTL overrides for specific UI components */
  .rtl .sidebar-item-icon {
    @apply mr-0 ml-3;
  }

  .rtl .header-search-icon {
    @apply left-auto right-3;
  }

  .rtl .header-search-input {
    @apply pl-3 pr-9;
  }

  /* For padding/margin flipping in RTL mode */
  .rtl .flip-pad-dir {
    @apply pl-0 pr-9;
  }

  .ltr .flip-pad-dir {
    @apply pr-0 pl-9;
  }

  /* For flipping icons like arrows in RTL mode */
  .rtl .flip-icon {
    @apply rotate-180;
  }

  /* Custom utility classes */
  .card-hover {
    @apply hover:shadow-lg transition-shadow duration-300;
  }

  .primary-gradient {
    @apply bg-gradient-to-r from-midnight-blue to-steel-gray;
  }

  .accent-gradient {
    @apply bg-gradient-to-r from-kalix-red to-kalix-red-dark;
  }

  .rtl .accent-gradient {
    @apply bg-gradient-to-l;
  }

  .stats-card {
    @apply bg-white rounded-lg border border-border/50 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300;
  }

  .glass-effect {
    @apply bg-white/90 backdrop-blur-sm border border-white/50 shadow-sm;
  }

  /* Animation fill mode utility */
  .animation-fill-mode-forwards {
    animation-fill-mode: forwards;
  }

  .animation-delay-100 {
    animation-delay: 100ms;
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-300 {
    animation-delay: 300ms;
  }

  /* Custom animation keyframes */
  @keyframes bounce-in {
    0% {
      transform: scale(0.95) translateX(0);
      opacity: 0;
    }
    70% {
      transform: scale(1.02) translateX(0);
      opacity: 1;
    }
    100% {
      transform: scale(1) translateX(0);
      opacity: 1;
    }
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-in {
    0% {
      transform: translateX(-20px);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .rtl @keyframes slide-in {
    0% {
      transform: translateX(20px);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes glow-pulse {
    0% {
      filter: drop-shadow(0 0 2px rgba(220, 38, 38, 0.4));
    }
    50% {
      filter: drop-shadow(0 0 8px rgba(220, 38, 38, 0.6));
    }
    100% {
      filter: drop-shadow(0 0 2px rgba(220, 38, 38, 0.4));
    }
  }

  @keyframes pulse-gentle {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes progress-animation {
    0% {
      width: 0%;
    }
    100% {
      width: 100%;
    }
  }

  /* Custom animation classes */
  .animate-bounce-in {
    animation: bounce-in 0.5s ease forwards;
  }

  .animate-fade-in {
    animation: fade-in 0.5s ease forwards;
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease forwards;
  }

  .animate-glow-pulse {
    animation: glow-pulse 2s infinite;
  }

  .animate-pulse-gentle {
    animation: pulse-gentle 2s ease-in-out infinite;
  }

  .animate-progress {
    animation: progress-animation 3s ease-in-out forwards;
  }
}

@layer components {
  /* Mobile-first responsive styles */
  .responsive-container {
    @apply w-full px-4 md:px-6 lg:px-8 mx-auto max-w-7xl;
  }

  .responsive-grid {
    @apply grid gap-4 sm:grid-cols-2 lg:grid-cols-4;
  }

  .responsive-flex {
    @apply flex flex-col sm:flex-row items-start sm:items-center gap-4;
  }

  /* RTL/LTR responsive adjustments */
  .rtl-flip {
    @apply rtl:space-x-reverse;
  }

  .rtl-padding {
    @apply rtl:pl-0 rtl:pr-4 ltr:pl-4 ltr:pr-0;
  }

  .rtl-margin {
    @apply rtl:mr-auto rtl:ml-0 ltr:ml-auto ltr:mr-0;
  }

  /* Table column resizing styles */
  .column-resize-handle {
    @apply absolute right-0 top-0 h-full w-2 cursor-col-resize select-none touch-none flex items-center justify-center transition-colors;
  }

  .column-resize-handle:hover::before {
    content: "";
    @apply absolute h-full w-0.5 bg-primary;
  }

  .column-resize-handle::after {
    content: "";
    @apply absolute h-full w-0.5 bg-muted-foreground/30;
  }

  /* Background patterns for public website */
  .bg-grid-pattern {
    background-size: 40px 40px;
    background-image:
      linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  }

  .bg-dots-pattern {
    background-size: 20px 20px;
    background-image: radial-gradient(rgba(0, 0, 0, 0.1) 2px, transparent 0);
  }

  .bg-diagonal-pattern {
    background-size: 10px 10px;
    background-image: repeating-linear-gradient(
      45deg,
      rgba(0, 0, 0, 0.05),
      rgba(0, 0, 0, 0.05) 1px,
      transparent 1px,
      transparent 10px
    );
  }
}

/* Animation delay utilities for success indicators */
.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}
